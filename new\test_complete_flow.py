#!/usr/bin/env python3
"""
完整流程测试 - 详尽的端到端测试
测试整个analyse流程并输出最终文献文件
"""

import asyncio
import logging
import sys
import os
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.analyse import AnalyseInterface

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_complete_flow():
    """完整流程测试"""
    logger.info("🎯 开始完整流程测试")
    logger.info("目标: 测试完整的analyse流程并输出最终文献文件")
    logger.info("="*80)
    
    try:
        # 初始化AnalyseInterface
        logger.info("🔧 初始化AnalyseInterface...")
        
        # 设置测试目录
        base_dir = "test_complete_flow"
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        
        analyse = AnalyseInterface(
            base_dir=base_dir,
            max_interaction_rounds=1,  # 简化交互
            llm_model="gemini-2.0-flash-thinking-exp-01-21"
        )
        logger.info("✅ AnalyseInterface初始化成功")
        
        # 测试参数
        topic = "深度学习"
        description = "深度学习的基本原理和应用"
        top_n = 3
        
        logger.info("📋 测试参数:")
        logger.info(f"   主题: {topic}")
        logger.info(f"   描述: {description}")
        logger.info(f"   目标文献数: {top_n}")
        logger.info(f"   输出目录: {base_dir}")
        logger.info("="*80)
        
        # 执行完整分析
        logger.info("🎯 开始执行完整analyse流程...")
        results = await analyse.analyse(
            topic=topic,
            description=description,
            top_n=top_n
        )
        
        logger.info("="*80)
        logger.info("📊 分析结果:")
        logger.info(f"   结果类型: {type(results)}")
        logger.info(f"   结果数量: {len(results) if isinstance(results, list) else 'N/A'}")
        
        # 详细分析结果
        if isinstance(results, list):
            for i, result in enumerate(results):
                logger.info(f"   结果 {i+1}:")
                logger.info(f"     类型: {type(result)}")
                if isinstance(result, dict):
                    logger.info(f"     字段: {list(result.keys())}")
                    if 'type' in result:
                        logger.info(f"     类型标识: {result['type']}")
                    if 'status' in result:
                        logger.info(f"     状态: {result['status']}")
                    if 'queries_generated' in result:
                        logger.info(f"     生成查询数: {result['queries_generated']}")
        
        # 检查输出文件
        logger.info("="*80)
        logger.info("📁 检查输出文件:")
        
        output_dir = Path(base_dir)
        if output_dir.exists():
            files = list(output_dir.glob("*"))
            logger.info(f"   输出目录: {output_dir.absolute()}")
            logger.info(f"   文件数量: {len(files)}")
            
            for file_path in files:
                logger.info(f"   📄 {file_path.name} ({file_path.stat().st_size} bytes)")
                
                # 如果是JSON文件，显示内容摘要
                if file_path.suffix == '.json':
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        if isinstance(data, list):
                            logger.info(f"      JSON内容: {len(data)} 个条目")
                            if data:
                                first_item = data[0]
                                if isinstance(first_item, dict):
                                    logger.info(f"      第一个条目字段: {list(first_item.keys())}")
                        elif isinstance(data, dict):
                            logger.info(f"      JSON内容: {list(data.keys())}")
                    except Exception as e:
                        logger.warning(f"      无法解析JSON: {e}")
                
                # 如果是文本文件，显示前几行
                elif file_path.suffix in ['.txt', '.md']:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()[:3]
                        logger.info(f"      文本内容预览: {len(lines)} 行")
                        for line in lines:
                            logger.info(f"        {line.strip()[:100]}...")
                    except Exception as e:
                        logger.warning(f"      无法读取文本: {e}")
        else:
            logger.warning(f"   输出目录不存在: {output_dir}")
        
        logger.info("="*80)
        logger.info("✅ 完整流程测试完成!")
        
        return results, output_dir
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None, None

if __name__ == "__main__":
    results, output_dir = asyncio.run(test_complete_flow())
    if results is not None:
        print("🎉 完整流程测试成功!")
        print(f"📁 输出目录: {output_dir}")
    else:
        print("❌ 完整流程测试失败!")
