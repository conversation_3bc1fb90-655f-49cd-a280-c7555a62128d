#!/usr/bin/env python3
"""
测试简化的MCP实现
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.llm_search_host import create_llm_search_host

async def test_simple_mcp():
    """测试简化的MCP搜索"""
    print("🧪 开始测试简化的MCP搜索")
    
    try:
        # 创建搜索主机
        host = create_llm_search_host()
        
        # 测试搜索
        topic = "transformer attention mechanism"
        description = "研究transformer模型中的注意力机制原理和优化方法"
        
        print(f"🎯 测试主题: {topic}")
        print(f"📝 描述: {description}")
        
        # 执行搜索
        results = await host.search(
            topic=topic,
            description=description,
            top_n=3  # 只要3篇文献进行测试
        )
        
        print(f"\n✅ 搜索完成!")
        print(f"📊 查询数量: {results.get('query_count', 0)}")
        print(f"📄 结果数量: {results.get('result_count', 0)}")
        
        if results.get('final_results'):
            print(f"\n📚 获得的文献:")
            for i, result in enumerate(results['final_results'][:3], 1):
                title = result.get('title', '未知标题')
                url = result.get('url', '未知URL')
                print(f"{i}. {title}")
                print(f"   URL: {url}")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == "__main__":
    result = asyncio.run(test_simple_mcp())
    print(f"\n🏁 测试结束")
