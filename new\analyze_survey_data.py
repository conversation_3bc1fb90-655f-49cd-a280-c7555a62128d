#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 survey_data_full_1231_one_line.jsonl 文件结构
"""

import json

def analyze_survey_data():
    """分析 survey data 的结构和内容"""
    
    # 读取 JSONL 文件
    with open('survey_data_full_1231_one_line.jsonl', 'r', encoding='utf-8') as f:
        line = f.readline().strip()
        data = json.loads(line)

    print('=== survey_data_full_1231_one_line.jsonl 文件结构分析 ===')
    print()

    # 分析每个 key 的详细信息
    for i, (key, value) in enumerate(data.items(), 1):
        print(f'{i:2d}. {key}')
        print(f'    类型: {type(value).__name__}')
        
        if isinstance(value, str):
            if len(value) > 100:
                print(f'    长度: {len(value)} 字符')
                print(f'    示例: "{value[:100]}..."')
            else:
                print(f'    值: "{value}"')
        elif isinstance(value, list):
            print(f'    长度: {len(value)} 个元素')
            if len(value) > 0:
                if isinstance(value[0], dict):
                    print(f'    元素类型: dict')
                    print(f'    第一个元素的 keys: {list(value[0].keys())}')
                else:
                    print(f'    元素类型: {type(value[0]).__name__}')
                    print(f'    前几个元素: {value[:3]}')
        elif isinstance(value, dict):
            print(f'    包含 keys: {list(value.keys())}')
        elif value is None:
            print(f'    值: None')
        else:
            print(f'    值: {value}')
        print()

    # 特别分析 papers 字段
    if 'papers' in data and data['papers']:
        print('=== papers 字段详细分析 ===')
        papers = data['papers']
        print(f'papers 数组包含 {len(papers)} 个论文对象')
        print()
        
        # 分析第一个 paper 对象的结构
        if papers:
            first_paper = papers[0]
            print('第一个 paper 对象的结构:')
            for key, value in first_paper.items():
                print(f'  - {key}: {type(value).__name__}')
                if isinstance(value, str) and len(value) > 50:
                    print(f'    示例: "{value[:50]}..."')
                elif value is not None:
                    print(f'    值: {value}')
            print()

    # 分析 outline 字段
    if 'outline' in data and data['outline']:
        print('=== outline 字段详细分析 ===')
        outline = data['outline']
        print(f'outline 包含 {len(outline)} 个章节标题:')
        for i, section in enumerate(outline, 1):
            print(f'  {i:2d}. {section}')
        print()

if __name__ == '__main__':
    analyze_survey_data()
