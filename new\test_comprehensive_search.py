#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 llm_search_host 的 comprehensive_search 功能
验证JSON格式输出和API配置
"""

import asyncio
import json
import sys
import os
import time

# 设置环境变量（确保API配置正确）
os.environ["SERP_API_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"
os.environ["OPENAI_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
os.environ["OPENAI_API_BASE"] = "https://api.shubiaobiao.cn/v1"
os.environ["GOOGLE_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"

# 添加路径以导入模块
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.search.llm_search_host import create_llm_search
    print("✅ 成功导入 llm_search_host 模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

async def test_comprehensive_search_direct():
    """直接测试comprehensive_search功能（绕过MCP）"""
    print("\n=== 测试 Comprehensive Search 功能 ===")
    
    try:
        # 创建LLM_search实例
        llm_search = create_llm_search()
        print(f"✅ LLM_search实例创建成功")
        
        # 测试参数
        topic = "transformer neural networks"
        description = "Research on transformer architecture in deep learning"
        top_n = 5
        similarity_threshold = 80
        min_length = 300
        max_length = 10000
        
        print(f"搜索主题: {topic}")
        print(f"描述: {description}")
        print(f"参数: top_n={top_n}, threshold={similarity_threshold}, min_length={min_length}, max_length={max_length}")
        
        start_time = time.time()
        
        # 调用comprehensive_search
        results = await llm_search.comprehensive_search(
            topic=topic,
            description=description,
            top_n=top_n,
            similarity_threshold=similarity_threshold,
            min_length=min_length,
            max_length=max_length
        )
        
        end_time = time.time()
        
        print(f"✅ Comprehensive search 完成，耗时: {end_time - start_time:.2f}秒")
        print(f"✅ 获得结果数量: {len(results)}")
        
        # 验证结果格式
        if results:
            print("\n=== 验证结果格式 ===")
            first_result = results[0]
            print(f"第一个结果的字段: {list(first_result.keys())}")
            
            # 检查必需字段
            required_fields = ['title', 'url', 'content', 'bibkey', 'abstract', 'similarity_score', 'token_count']
            missing_fields = [field for field in required_fields if field not in first_result]
            
            if missing_fields:
                print(f"❌ 缺少字段: {missing_fields}")
                return False
            else:
                print("✅ 所有必需字段都存在")
            
            # 显示前几个结果的摘要
            print("\n=== 结果摘要 ===")
            for i, result in enumerate(results[:3], 1):
                print(f"{i}. 标题: {result['title'][:60]}...")
                print(f"   URL: {result['url']}")
                print(f"   相似度: {result['similarity_score']}")
                print(f"   Token数: {result['token_count']}")
                print(f"   摘要: {result['abstract'][:100]}...")
                print()
            
            # 验证JSON序列化
            try:
                json_str = json.dumps(results, ensure_ascii=False, indent=2)
                print("✅ 结果可以正确序列化为JSON")
                print(f"JSON大小: {len(json_str)} 字符")
            except Exception as e:
                print(f"❌ JSON序列化失败: {e}")
                return False
            
            return True
        else:
            print("❌ 没有获得任何结果")
            return False
            
    except Exception as e:
        print(f"❌ Comprehensive search 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_configuration():
    """测试API配置是否正确导入"""
    print("\n=== 测试API配置 ===")
    
    try:
        # 检查环境变量
        api_keys = {
            "SERP_API_KEY": os.environ.get("SERP_API_KEY"),
            "OPENAI_API_KEY": os.environ.get("OPENAI_API_KEY"),
            "OPENAI_API_BASE": os.environ.get("OPENAI_API_BASE"),
            "GOOGLE_API_KEY": os.environ.get("GOOGLE_API_KEY")
        }
        
        print("环境变量配置:")
        for key, value in api_keys.items():
            if value:
                masked_value = value[:8] + "..." + value[-8:] if len(value) > 16 else value
                print(f"  {key}: {masked_value}")
            else:
                print(f"  {key}: 未设置")
        
        # 检查配置文件
        config_file = "config/llm_search_mcp_config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件存在: {config_file}")
            print(f"配置文件包含的服务器: {list(config.keys())}")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False

async def test_memory_functionality():
    """测试内存功能"""
    print("\n=== 测试内存功能 ===")
    
    try:
        llm_search = create_llm_search()
        
        # 获取内存统计
        stats = llm_search.get_memory_statistics()
        print(f"✅ 内存统计: {len(stats)} 个模型的历史记录")
        
        # 获取对话历史
        history = llm_search.get_conversation_history(limit=5)
        print(f"✅ 对话历史: {len(history)} 条记录")
        
        if history:
            print("最近的对话:")
            for i, conv in enumerate(history[-3:], 1):
                print(f"  {i}. {conv.get('timestamp', 'N/A')}: {conv.get('messages', [{}])[0].get('content', 'N/A')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存功能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=== LLM Search Host Comprehensive Search 测试 ===")
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试API配置
    test_results["api_configuration"] = await test_api_configuration()
    
    # 2. 测试内存功能
    test_results["memory_functionality"] = await test_memory_functionality()
    
    # 3. 测试comprehensive_search功能
    test_results["comprehensive_search"] = await test_comprehensive_search_direct()
    
    # 输出测试总结
    print("\n=== 测试总结 ===")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(test_results.values())
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 llm_search_host 可以正确调用并返回正确的JSON格式！")
        print("✅ API配置正确导入")
        print("✅ JSON格式兼容LLMxMapReduce_V2和Survey数据结构")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
