#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的 JSON 格式兼容性
"""

import asyncio
import json
import sys
import os

# 添加路径以导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'search'))

try:
    from llm_search_mcp_server import (
        proc_title_to_str,
        estimate_tokens,
        extract_abstract,
        _process_and_sort_results
    )
    print("✅ 成功导入增强的辅助函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_helper_functions():
    """测试辅助函数"""
    print("\n=== 测试辅助函数 ===")
    
    # 测试 proc_title_to_str
    test_titles = [
        "Machine Learning: A Comprehensive Survey",
        "Deep Learning for Computer Vision",
        "Attention Is All You Need",
        "BERT: Pre-training of Deep Bidirectional Transformers"
    ]
    
    print("1. 测试 proc_title_to_str:")
    for title in test_titles:
        bibkey = proc_title_to_str(title)
        print(f"   '{title}' → '{bibkey}'")
    
    # 测试 estimate_tokens
    test_text = "This is a sample text for testing token estimation. " * 10
    tokens = estimate_tokens(test_text)
    print(f"\n2. 测试 estimate_tokens:")
    print(f"   文本长度: {len(test_text)} 字符")
    print(f"   单词数: {len(test_text.split())} 个")
    print(f"   估算token数: {tokens}")
    
    # 测试 extract_abstract
    long_text = "This is the beginning of a long article. " * 20
    abstract = extract_abstract(long_text, 100)
    print(f"\n3. 测试 extract_abstract:")
    print(f"   原文长度: {len(long_text)} 字符")
    print(f"   摘要长度: {len(abstract)} 字符")
    print(f"   摘要内容: {abstract}")

def test_format_compatibility():
    """测试格式兼容性"""
    print("\n=== 测试格式兼容性 ===")
    
    # 模拟爬虫结果数据
    mock_results = [
        {
            "url": "https://example.com/paper1",
            "title": "Machine Learning: A Comprehensive Survey",
            "content": "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. " * 10,
            "similarity_score": 85.5,
            "topic": "machine learning"
        },
        {
            "url": "https://example.com/paper2", 
            "title": "Deep Learning for Computer Vision",
            "content": "Deep learning has revolutionized computer vision by enabling machines to automatically learn hierarchical representations from raw pixel data. " * 8,
            "similarity_score": 78.2,
            "topic": "machine learning"
        }
    ]
    
    # 使用 _process_and_sort_results 处理数据
    formatted_results = _process_and_sort_results(
        scored_results=mock_results,
        top_n=5,
        similarity_threshold=70,
        min_length=100,
        max_length=10000
    )
    
    print(f"处理结果数量: {len(formatted_results)}")
    
    if formatted_results:
        print("\n第一个结果的格式:")
        first_result = formatted_results[0]
        print(json.dumps(first_result, ensure_ascii=False, indent=2))
        
        # 检查字段兼容性
        print("\n字段兼容性检查:")
        
        # LLMxMapReduce_V2 期望字段
        llm_mapreduce_fields = {'title', 'url', 'txt', 'similarity'}
        # Survey 期望字段
        survey_fields = {'title', 'url', 'txt', 'bibkey', 'abstract', 'txt_token'}
        
        current_fields = set(first_result.keys())
        
        print(f"当前字段: {sorted(current_fields)}")
        print(f"LLMxMapReduce_V2期望: {sorted(llm_mapreduce_fields)}")
        print(f"Survey期望: {sorted(survey_fields)}")
        
        # 检查兼容性
        llm_missing = llm_mapreduce_fields - current_fields
        survey_missing = survey_fields - current_fields
        
        if not llm_missing:
            print("✅ 完全兼容 LLMxMapReduce_V2 格式")
        else:
            print(f"❌ 缺少 LLMxMapReduce_V2 字段: {llm_missing}")
            
        if not survey_missing:
            print("✅ 完全兼容 Survey 格式")
        else:
            print(f"❌ 缺少 Survey 字段: {survey_missing}")

def test_jsonl_format():
    """测试 JSONL 格式输出"""
    print("\n=== 测试 JSONL 格式 ===")
    
    # 模拟完整的爬虫结果
    mock_crawl_result = {
        "title": "machine learning research",
        "papers": [
            {
                "title": "Machine Learning: A Comprehensive Survey",
                "url": "https://example.com/paper1",
                "txt": "Machine learning content...",
                "similarity": 85.5,
                "bibkey": "machine_learning_a_comprehensive_survey",
                "abstract": "Machine learning is a subset...",
                "txt_token": 150,
                "txt_length": 500,
                "source_type": "web_crawl",
                "crawl_timestamp": 1640995200.0
            },
            {
                "title": "Deep Learning for Computer Vision", 
                "url": "https://example.com/paper2",
                "txt": "Deep learning content...",
                "similarity": 78.2,
                "bibkey": "deep_learning_for_computer_vision",
                "abstract": "Deep learning has revolutionized...",
                "txt_token": 120,
                "txt_length": 400,
                "source_type": "web_crawl",
                "crawl_timestamp": 1640995200.0
            }
        ]
    }
    
    print("JSONL 格式示例:")
    print(json.dumps(mock_crawl_result, ensure_ascii=False, indent=2))
    
    # 保存为 JSONL 文件
    with open("test_enhanced_format_output.jsonl", "w", encoding="utf-8") as f:
        json.dump(mock_crawl_result, f, ensure_ascii=False)
    
    print("\n✅ JSONL 格式文件已保存到 test_enhanced_format_output.jsonl")

def compare_with_survey_data():
    """与 survey_data 格式对比"""
    print("\n=== 与 Survey Data 格式对比 ===")
    
    # 检查是否存在 survey_data 文件
    survey_file = "survey_data_full_1231_one_line.jsonl"
    if os.path.exists(survey_file):
        try:
            with open(survey_file, "r", encoding="utf-8") as f:
                survey_data = json.load(f)
            
            if "papers" in survey_data and survey_data["papers"]:
                survey_paper = survey_data["papers"][0]
                print("Survey 数据格式示例:")
                print(json.dumps(survey_paper, ensure_ascii=False, indent=2))
                
                survey_fields = set(survey_paper.keys())
                print(f"\nSurvey 数据字段: {sorted(survey_fields)}")
                
        except Exception as e:
            print(f"读取 survey 数据失败: {e}")
    else:
        print(f"未找到 {survey_file} 文件")

def main():
    """主测试函数"""
    print("=== 增强格式兼容性测试 ===")
    
    # 测试辅助函数
    test_helper_functions()
    
    # 测试格式兼容性
    test_format_compatibility()
    
    # 测试 JSONL 格式
    test_jsonl_format()
    
    # 与 survey_data 对比
    compare_with_survey_data()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
