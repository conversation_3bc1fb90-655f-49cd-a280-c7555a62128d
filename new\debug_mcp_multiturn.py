#!/usr/bin/env python3
"""
调试MCP多轮执行的问题
"""

import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.search.llm_search_mcp_client import MCPClient

async def debug_mcp_multiturn():
    """调试MCP多轮执行"""
    print("🔧 开始调试MCP多轮执行...")
    
    try:
        # 加载MCP配置
        import json
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'llm_search_mcp_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            full_config = json.load(f)

        # 提取服务器配置
        server_config = full_config["mcpServers"]["llm_search_mcp"]

        # 初始化MCP客户端
        client = MCPClient(server_config)
        
        # 连接到MCP服务器
        print("📡 连接MCP服务器...")
        await client.connect()
        
        # 获取可用工具
        print("🔍 获取可用工具...")
        tools = await client.list_tools()
        print(f"📋 可用工具: {[tool['name'] for tool in tools]}")
        
        # 测试第一步：生成搜索查询
        print("\n🔧 测试步骤1: 生成搜索查询...")
        query_result = await client.call_tool("generate_search_queries", {
            "topic": "机器学习基础",
            "description": "机器学习的基本概念、算法和应用"
        })
        
        if query_result:
            print(f"✅ 查询生成成功: {len(query_result.get('queries', []))} 个查询")
            queries = query_result.get('queries', [])[:3]  # 只取前3个查询进行测试
            print(f"📝 测试查询: {queries}")
            
            # 测试第二步：网络搜索
            print("\n🔧 测试步骤2: 网络搜索...")
            print(f"🔑 检查环境变量:")
            print(f"   SERP_API_KEY: {'已设置' if os.getenv('SERP_API_KEY') else '未设置'}")
            print(f"   SERPAPI_KEY: {'已设置' if os.getenv('SERPAPI_KEY') else '未设置'}")
            print(f"   BING_SEARCH_V7_SUBSCRIPTION_KEY: {'已设置' if os.getenv('BING_SEARCH_V7_SUBSCRIPTION_KEY') else '未设置'}")

            search_result = await client.call_tool("web_search", {
                "queries": queries,
                "topic": "机器学习基础"
            })

            print(f"🔍 搜索结果详情: {search_result}")

            if search_result:
                urls = search_result.get('urls', [])
                print(f"✅ 搜索成功: {len(urls)} 个URL")
                print(f"📝 测试URL: {urls[:3]}")
                
                if urls:
                    # 测试第三步：分析搜索结果
                    print("\n🔧 测试步骤3: 分析搜索结果...")
                    analyze_result = await client.call_tool("analyze_search_results", {
                        "urls": urls[:5],  # 只分析前5个URL
                        "topic": "机器学习基础",
                        "max_results": 3
                    })
                    
                    if analyze_result:
                        analyzed_urls = analyze_result.get('analyzed_urls', [])
                        print(f"✅ 分析成功: {len(analyzed_urls)} 个分析结果")
                        print(f"📝 分析URL: {analyzed_urls}")
                        
                        if analyzed_urls:
                            # 测试第四步：爬取内容
                            print("\n🔧 测试步骤4: 爬取内容...")
                            crawl_result = await client.call_tool("crawl_urls", {
                                "topic": "机器学习基础",
                                "url_list": analyzed_urls[:2],  # 只爬取前2个URL
                                "top_n": 2
                            })
                            
                            if crawl_result:
                                crawled_data = crawl_result.get('crawled_data', [])
                                print(f"✅ 爬取成功: {len(crawled_data)} 个内容")
                                
                                # 显示最终结果
                                print("\n📊 最终结果:")
                                for i, item in enumerate(crawled_data[:2], 1):
                                    print(f"  {i}. {item.get('title', 'No title')}")
                                    print(f"     URL: {item.get('url', 'No URL')}")
                                    print(f"     相似度: {item.get('similarity_score', 'N/A')}")
                            else:
                                print("❌ 爬取失败")
                        else:
                            print("⚠️ 没有分析出有效的URL")
                    else:
                        print("❌ 分析失败")
                else:
                    print("⚠️ 没有搜索到URL")
            else:
                print("❌ 搜索失败")
        else:
            print("❌ 查询生成失败")
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        try:
            await client.disconnect()
            print("🔌 MCP连接已断开")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(debug_mcp_multiturn())
