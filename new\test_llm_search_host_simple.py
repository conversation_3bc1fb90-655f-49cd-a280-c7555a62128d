#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 llm_search_host 测试
直接测试核心功能，不通过MCP协议
"""

import asyncio
import json
import sys
import os
import time

# 设置环境变量（确保API配置正确）
os.environ["SERP_API_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"
os.environ["OPENAI_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
os.environ["OPENAI_API_BASE"] = "https://api.shubiaobiao.cn/v1"
os.environ["GOOGLE_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"

# 添加路径以导入模块
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.search.llm_search_host import LLM_search, create_llm_search
    print("✅ 成功导入 llm_search_host 模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_host_initialization():
    """测试Host初始化"""
    print("\n=== 测试Host初始化 ===")
    
    try:
        # 创建LLM_search实例
        llm_search = create_llm_search()
        print(f"✅ LLM_search实例创建成功")
        print(f"模型: {llm_search.model}")
        print(f"推理类型: {llm_search.infer_type}")
        print(f"内存功能: {llm_search.use_memory}")
        
        # 测试内存功能
        stats = llm_search.get_memory_statistics()
        print(f"✅ 内存统计: {len(stats)} 个模型的历史记录")
        
        return True
        
    except Exception as e:
        print(f"❌ Host初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_llm_search():
    """直接测试LLM搜索功能（绕过MCP）"""
    print("\n=== 测试直接LLM搜索功能 ===")
    
    try:
        # 导入LLM_search类（绕过MCP）
        from src.search.LLM_search import LLM_search as DirectLLMSearch
        
        # 创建直接的LLM搜索实例
        direct_search = DirectLLMSearch(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI"
        )
        print("✅ 直接LLM搜索实例创建成功")
        
        # 执行简单搜索测试
        topic = "machine learning"
        print(f"开始搜索主题: {topic}")
        
        start_time = time.time()
        
        # 测试查询生成
        queries = direct_search.get_queries(topic, description="")
        print(f"✅ 生成查询: {len(queries)} 个")
        for i, query in enumerate(queries[:3], 1):
            print(f"  {i}. {query}")

        # 测试网络搜索
        if queries:
            # 测试单个查询搜索
            search_result = direct_search.web_search(queries[0])
            print(f"✅ 单个查询搜索: 获得结果")

            # 测试批量搜索
            urls = direct_search.batch_web_search(queries[:2], topic, top_n=5)
            print(f"✅ 批量网络搜索: 获得 {len(urls)} 个URL")
            for i, url in enumerate(urls[:3], 1):
                print(f"  {i}. {url}")
        
        end_time = time.time()
        print(f"✅ 直接搜索测试完成，耗时: {end_time - start_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_host_request_wrapper():
    """测试Host的RequestWrapper功能"""
    print("\n=== 测试Host RequestWrapper ===")
    
    try:
        llm_search = create_llm_search()
        
        # 测试简单的LLM请求
        messages = [{"role": "user", "content": "Hello, this is a test message. Please respond with 'Test successful'."}]
        
        print("发送测试消息到LLM...")
        # 使用正确的参数调用async_request
        response = await llm_search.request_wrapper.async_request(messages)
        
        print(f"✅ LLM响应: {response[:100]}...")
        
        # 测试对话历史
        history = llm_search.get_conversation_history(limit=1)
        print(f"✅ 对话历史: {len(history)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ RequestWrapper测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_format_compatibility():
    """测试JSON格式兼容性"""
    print("\n=== 测试JSON格式兼容性 ===")
    
    try:
        # 模拟爬虫结果
        mock_crawl_result = {
            "title": "Test Paper on Machine Learning",
            "url": "https://example.com/paper",
            "content": "This is a test paper about machine learning algorithms and their applications in various domains.",
            "similarity_score": 85.5,
            "token_count": 150
        }
        
        # 测试格式转换函数
        from src.search.llm_search_mcp_server import proc_title_to_str, estimate_tokens, extract_abstract
        
        # 测试bibkey生成
        bibkey = proc_title_to_str(mock_crawl_result["title"])
        print(f"✅ Bibkey生成: {bibkey}")
        
        # 测试token估算
        tokens = estimate_tokens(mock_crawl_result["content"])
        print(f"✅ Token估算: {tokens}")
        
        # 测试摘要提取
        abstract = extract_abstract(mock_crawl_result["content"])
        print(f"✅ 摘要提取: {abstract[:50]}...")
        
        # 测试完整格式转换
        formatted_result = {
            "title": mock_crawl_result["title"],
            "url": mock_crawl_result["url"],
            "content": mock_crawl_result["content"],
            "bibkey": bibkey,
            "abstract": abstract,
            "similarity_score": mock_crawl_result["similarity_score"],
            "token_count": tokens
        }
        
        print("✅ JSON格式兼容性测试通过")
        print(f"格式化结果字段: {list(formatted_result.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON格式兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("=== LLM Search Host 简化功能测试 ===")
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试Host初始化
    test_results["host_initialization"] = test_host_initialization()
    
    # 2. 测试RequestWrapper功能
    test_results["request_wrapper"] = await test_host_request_wrapper()
    
    # 3. 测试直接LLM搜索功能
    test_results["direct_llm_search"] = await test_direct_llm_search()
    
    # 4. 测试JSON格式兼容性
    test_results["json_format"] = test_json_format_compatibility()
    
    # 输出测试总结
    print("\n=== 测试总结 ===")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(test_results.values())
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
