#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比分析 llm_search_mcp_server 的爬虫结果与 survey_data JSON 文件的数据结构
"""

import json

def analyze_mcp_server_structure():
    """分析 MCP 服务器的数据结构"""
    print("=== MCP 服务器爬虫结果数据结构分析 ===")
    print()
    
    # 基于代码分析的 MCP 服务器数据结构
    mcp_structure = {
        "crawl_urls_result": {
            "topic": "str - 研究主题",
            "total_urls": "int - 总URL数量",
            "crawl_results": "int - 爬取结果数量",
            "filtered_results": "int - 过滤后结果数量", 
            "scored_results": "int - 评分后结果数量",
            "final_results": "List[Dict] - 最终结果列表",
            "final_count": "int - 最终结果数量",
            "processing_metadata": {
                "model": "str - 使用的模型",
                "similarity_threshold": "float - 相似度阈值",
                "min_length": "int - 最小长度",
                "max_length": "int - 最大长度",
                "top_n": "int - 返回数量",
                "total_time": "float - 总处理时间",
                "success": "bool - 是否成功"
            }
        },
        "final_results_item": {
            "rank": "int - 排名",
            "url": "str - URL地址",
            "title": "str - 标题",
            "content": "str - 内容",
            "similarity_score": "int - 相似度评分",
            "content_length": "int - 内容长度",
            "topic": "str - 主题"
        },
        "web_search_result": {
            "topic": "str - 主题",
            "queries": "List[str] - 查询列表",
            "engine": "str - 搜索引擎",
            "urls": "List[str] - URL列表",
            "url_count": "int - URL数量",
            "top_n": "int - 返回数量",
            "processing_metadata": {
                "engine": "str - 搜索引擎",
                "query_count": "int - 查询数量",
                "result_count": "int - 结果数量",
                "method": "str - 方法"
            }
        }
    }
    
    print("1. crawl_urls 工具返回结构:")
    for key, desc in mcp_structure["crawl_urls_result"].items():
        if isinstance(desc, dict):
            print(f"  {key}:")
            for sub_key, sub_desc in desc.items():
                print(f"    {sub_key}: {sub_desc}")
        else:
            print(f"  {key}: {desc}")
    
    print("\n2. final_results 中每个项目的结构:")
    for key, desc in mcp_structure["final_results_item"].items():
        print(f"  {key}: {desc}")
    
    print("\n3. web_search 工具返回结构:")
    for key, desc in mcp_structure["web_search_result"].items():
        if isinstance(desc, dict):
            print(f"  {key}:")
            for sub_key, sub_desc in desc.items():
                print(f"    {sub_key}: {sub_desc}")
        else:
            print(f"  {key}: {desc}")
    
    return mcp_structure

def analyze_survey_data_structure():
    """分析 survey_data JSON 文件的数据结构"""
    print("\n=== Survey Data JSON 文件数据结构分析 ===")
    print()
    
    # 读取实际的 survey data
    with open('survey_data_full_1231_one_line.jsonl', 'r', encoding='utf-8') as f:
        line = f.readline().strip()
        data = json.loads(line)
    
    survey_structure = {}
    
    print("1. 主要字段:")
    for key, value in data.items():
        if key == "papers":
            print(f"  {key}: List[Dict] - 包含 {len(value)} 个论文对象")
            survey_structure[key] = f"List[Dict] - {len(value)} papers"
        elif key == "outline":
            print(f"  {key}: List[str] - 包含 {len(value)} 个章节")
            survey_structure[key] = f"List[str] - {len(value)} sections"
        elif isinstance(value, str):
            if len(value) > 100:
                print(f"  {key}: str - 长度 {len(value)} 字符")
                survey_structure[key] = f"str - {len(value)} chars"
            else:
                print(f"  {key}: str - '{value}'")
                survey_structure[key] = f"str - '{value}'"
        else:
            print(f"  {key}: {type(value).__name__} - {value}")
            survey_structure[key] = f"{type(value).__name__} - {value}"
    
    print("\n2. papers 字段中每个论文对象的结构:")
    if data["papers"]:
        first_paper = data["papers"][0]
        paper_structure = {}
        for key, value in first_paper.items():
            if isinstance(value, str):
                if len(value) > 50:
                    print(f"  {key}: str - 长度 {len(value)} 字符")
                    paper_structure[key] = f"str - {len(value)} chars"
                else:
                    print(f"  {key}: str - '{value}'")
                    paper_structure[key] = f"str - '{value}'"
            else:
                print(f"  {key}: {type(value).__name__} - {value}")
                paper_structure[key] = f"{type(value).__name__} - {value}"
        
        survey_structure["paper_item"] = paper_structure
    
    return survey_structure

def compare_structures(mcp_structure, survey_structure):
    """对比两种数据结构"""
    print("\n=== 数据结构对比分析 ===")
    print()
    
    print("1. 字段映射关系:")
    print("   MCP crawl_urls final_results -> Survey papers")
    print("   ┌─────────────────────────────┬─────────────────────────────┐")
    print("   │ MCP final_results_item      │ Survey paper_item           │")
    print("   ├─────────────────────────────┼─────────────────────────────┤")
    print("   │ rank                        │ (无对应字段)                │")
    print("   │ url                         │ url, pdf_url, latex_url     │")
    print("   │ title                       │ title                       │")
    print("   │ content                     │ txt, latex, abstract        │")
    print("   │ similarity_score            │ (无对应字段)                │")
    print("   │ content_length              │ txt_length, latex_length    │")
    print("   │ topic                       │ (无对应字段)                │")
    print("   └─────────────────────────────┴─────────────────────────────┘")
    
    print("\n2. 主要差异:")
    print("   a) 内容丰富度:")
    print("      - Survey papers: 包含完整的学术论文信息(作者、摘要、引言、参考文献等)")
    print("      - MCP results: 主要包含爬取的网页内容和相似度评分")
    
    print("\n   b) 数据来源:")
    print("      - Survey papers: 来自学术论文数据库(arXiv等)")
    print("      - MCP results: 来自网络搜索和爬虫")
    
    print("\n   c) 结构复杂度:")
    print("      - Survey papers: 25个字段，包含详细的学术元数据")
    print("      - MCP results: 7个字段，专注于内容和相关性")
    
    print("\n   d) 评分机制:")
    print("      - Survey papers: 无相似度评分，依赖引用关系")
    print("      - MCP results: 包含LLM生成的相似度评分(0-100)")

def suggest_integration():
    """建议数据结构整合方案"""
    print("\n=== 数据结构整合建议 ===")
    print()
    
    print("1. 统一数据结构建议:")
    print("   为了使 MCP 爬虫结果与 Survey 数据兼容，建议扩展 MCP 结果结构:")
    
    suggested_structure = {
        "title": "从 MCP title 字段获取",
        "authors": "从网页内容中提取或设为 None",
        "bibkey": "基于 title 生成",
        "url": "从 MCP url 字段获取",
        "abstract": "从 MCP content 中提取摘要部分",
        "abstract_length": "计算摘要长度",
        "pdf_url": "如果是PDF链接则使用 url，否则为 None",
        "pdf_path": "本地保存路径",
        "txt": "从 MCP content 字段获取",
        "txt_length": "从 MCP content_length 获取",
        "similarity_score": "从 MCP similarity_score 获取(新增字段)",
        "crawl_rank": "从 MCP rank 获取(新增字段)",
        "crawl_topic": "从 MCP topic 获取(新增字段)"
    }
    
    for key, desc in suggested_structure.items():
        print(f"   {key}: {desc}")
    
    print("\n2. 实现建议:")
    print("   a) 在 llm_search_mcp_server 中添加数据转换函数")
    print("   b) 扩展 Survey 类以支持 MCP 爬虫数据")
    print("   c) 保持向后兼容性，支持两种数据格式")

def main():
    """主函数"""
    mcp_structure = analyze_mcp_server_structure()
    survey_structure = analyze_survey_data_structure()
    compare_structures(mcp_structure, survey_structure)
    suggest_integration()

if __name__ == '__main__':
    main()
