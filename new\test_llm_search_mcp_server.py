#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 llm_search_mcp_server 的 SERP 搜索和爬虫功能
"""

import asyncio
import json
import sys
import os

# 设置环境变量（从配置文件读取）
os.environ["SERP_API_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"
os.environ["OPENAI_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
os.environ["OPENAI_API_BASE"] = "https://api.shubiaobiao.cn/v1"
os.environ["GOOGLE_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"

# 添加路径以导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'search'))

try:
    from llm_search_mcp_server import (
        _generate_search_queries,
        _web_search,
        _crawl_urls,
        SERVER_CONFIG
    )
    print("✅ 成功导入 llm_search_mcp_server 模块")
except ImportError as e:
    print(f"❌ 导入 llm_search_mcp_server 失败: {e}")
    sys.exit(1)

async def test_search_query_generation():
    """测试搜索查询生成功能"""
    print("\n=== 测试搜索查询生成 ===")
    
    try:
        result = await _generate_search_queries(
            topic="machine learning",
            description="深度学习和神经网络相关研究"
        )
        
        print(f"✅ 查询生成成功")
        print(f"主题: {result['topic']}")
        print(f"生成的查询数量: {result['query_count']}")
        print(f"查询列表: {result['queries']}")
        print(f"使用模型: {result['processing_metadata']['model']}")
        
        return result['queries']
        
    except Exception as e:
        print(f"❌ 查询生成失败: {e}")
        return ["machine learning", "deep learning", "neural networks"]

async def test_web_search(queries):
    """测试网络搜索功能"""
    print("\n=== 测试网络搜索 ===")
    
    try:
        # 只使用前3个查询进行测试
        test_queries = queries[:3] if len(queries) > 3 else queries
        
        result = await _web_search(
            queries=test_queries,
            topic="machine learning",
            top_n=10
        )
        
        print(f"✅ 网络搜索成功")
        print(f"搜索引擎: {result['engine']}")
        print(f"查询数量: {result['processing_metadata']['query_count']}")
        print(f"获得URL数量: {result['url_count']}")
        print(f"前5个URL:")
        for i, url in enumerate(result['urls'][:5], 1):
            print(f"  {i}. {url}")
        
        return result['urls'][:5]  # 返回前5个URL用于爬虫测试
        
    except Exception as e:
        print(f"❌ 网络搜索失败: {e}")
        # 返回一些测试URL
        return [
            "https://en.wikipedia.org/wiki/Machine_learning",
            "https://www.nature.com/articles/nature14539",
            "https://arxiv.org/abs/1706.03762"
        ]

async def test_crawl_urls(urls):
    """测试URL爬取功能"""
    print("\n=== 测试URL爬取 ===")
    
    try:
        # 只爬取前2个URL进行测试
        test_urls = urls[:2] if len(urls) > 2 else urls
        
        result = await _crawl_urls(
            topic="machine learning",
            url_list=test_urls,
            top_n=5,
            similarity_threshold=60,  # 降低阈值
            min_length=200,
            max_length=50000  # 增加最大长度
        )
        
        print(f"✅ URL爬取成功")
        print(f"总URL数: {result['total_urls']}")
        print(f"爬取结果数: {result['crawl_results']}")
        print(f"过滤结果数: {result['filtered_results']}")
        print(f"评分结果数: {result['scored_results']}")
        print(f"最终结果数: {result['final_count']}")
        print(f"处理时间: {result['processing_metadata']['total_time']:.2f}秒")
        
        if result['final_results']:
            print(f"\n前3个爬取结果:")
            for i, paper in enumerate(result['final_results'][:3], 1):
                print(f"  {i}. 标题: {paper['title'][:60]}...")
                print(f"     URL: {paper['url']}")
                print(f"     相似度: {paper['similarity_score']}")
                print(f"     内容长度: {paper['content_length']}")
                print()
        
        return result
        
    except Exception as e:
        print(f"❌ URL爬取失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return None

async def test_json_format_compatibility(crawl_result):
    """测试JSON格式兼容性"""
    print("\n=== 测试JSON格式兼容性 ===")
    
    if not crawl_result or not crawl_result['final_results']:
        print("❌ 没有爬取结果可供测试")
        return
    
    try:
        # 检查当前格式
        current_format = crawl_result['final_results'][0]
        print("当前格式字段:")
        for key, value in current_format.items():
            print(f"  - {key}: {type(value).__name__}")
        
        # 检查是否包含必需的字段
        required_fields = ['title', 'url', 'content', 'similarity_score']
        missing_fields = [field for field in required_fields if field not in current_format]
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
        else:
            print("✅ 包含所有必需字段")
        
        # 检查是否需要添加兼容字段
        survey_fields = ['bibkey', 'abstract', 'txt_token', 'txt_length']
        missing_survey_fields = [field for field in survey_fields if field not in current_format]
        
        if missing_survey_fields:
            print(f"⚠️ 缺少Survey兼容字段: {missing_survey_fields}")
            print("需要在 _process_and_sort_results 函数中添加这些字段")
        else:
            print("✅ 包含所有Survey兼容字段")
            
    except Exception as e:
        print(f"❌ JSON格式兼容性测试失败: {e}")

async def main():
    """主测试函数"""
    print("=== LLM Search MCP Server 功能测试 ===")
    print(f"服务器配置: {SERVER_CONFIG}")
    
    # 1. 测试查询生成
    queries = await test_search_query_generation()
    
    # 2. 测试网络搜索
    urls = await test_web_search(queries)
    
    # 3. 测试URL爬取
    crawl_result = await test_crawl_urls(urls)
    
    # 4. 测试JSON格式兼容性
    await test_json_format_compatibility(crawl_result)
    
    print("\n=== 测试完成 ===")
    
    # 保存测试结果
    if crawl_result:
        with open("test_crawl_result.json", "w", encoding="utf-8") as f:
            json.dump(crawl_result, f, ensure_ascii=False, indent=2)
        print("✅ 测试结果已保存到 test_crawl_result.json")

if __name__ == "__main__":
    asyncio.run(main())
