#!/usr/bin/env python3
"""
直接测试LLM_search类
验证底层LLM_search是否能正常工作
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_llm_search_direct():
    """直接测试LLM_search类"""
    logger.info("🎯 开始直接测试LLM_search类")
    logger.info("="*80)
    
    try:
        # 导入LLM_search
        logger.info("📦 导入LLM_search...")
        try:
            from LLM_search import LLM_search
        except ImportError:
            try:
                sys.path.append(os.path.join(os.path.dirname(__file__), 'LLMxMapReduce_V2', 'src'))
                from LLM_search import LLM_search
            except ImportError:
                logger.error("❌ 无法导入LLM_search")
                return False
        
        logger.info("✅ LLM_search导入成功")
        
        # 初始化LLM_search
        logger.info("🔧 初始化LLM_search...")
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            engine="google",
            each_query_result=20
        )
        logger.info("✅ LLM_search初始化成功")
        
        # 测试get_queries方法
        logger.info("🧪 测试get_queries方法...")
        topic = "深度学习"
        description = "深度学习的基本原理"
        
        queries = llm_search.get_queries(topic=topic, description=description)
        logger.info(f"📋 查询结果类型: {type(queries)}")
        logger.info(f"📋 查询结果: {queries}")
        
        if queries is None:
            logger.error("❌ get_queries返回None")
            return False
        elif isinstance(queries, list) and len(queries) > 0:
            logger.info(f"✅ get_queries成功，生成了{len(queries)}个查询")
            for i, query in enumerate(queries[:3]):
                logger.info(f"   查询{i+1}: {query}")
        else:
            logger.warning(f"⚠️ get_queries返回了意外的结果: {queries}")
        
        # 测试batch_web_search方法
        if queries and isinstance(queries, list) and len(queries) > 0:
            logger.info("🧪 测试batch_web_search方法...")
            test_queries = queries[:2]  # 只用前2个查询
            
            urls = llm_search.batch_web_search(queries=test_queries, topic=topic, top_n=5)
            logger.info(f"🔗 搜索结果类型: {type(urls)}")
            logger.info(f"🔗 搜索结果: {urls}")
            
            if urls is None:
                logger.error("❌ batch_web_search返回None")
                return False
            elif isinstance(urls, list) and len(urls) > 0:
                logger.info(f"✅ batch_web_search成功，找到了{len(urls)}个URL")
                for i, url in enumerate(urls[:3]):
                    logger.info(f"   URL{i+1}: {url}")
            else:
                logger.warning(f"⚠️ batch_web_search返回了意外的结果: {urls}")
        
        logger.info("="*80)
        logger.info("✅ 直接测试LLM_search完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_llm_search_direct()
    if success:
        print("🎉 LLM_search直接测试成功!")
    else:
        print("❌ LLM_search直接测试失败!")
