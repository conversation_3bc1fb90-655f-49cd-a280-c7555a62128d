#!/usr/bin/env python3
"""
直接测试MCP服务器的web_search工具
"""
import asyncio
import json
import sys
import os

# 添加项目路径并设置工作目录
sys.path.insert(0, os.path.abspath("."))
os.chdir(os.path.abspath("."))

from src.search.llm_search_mcp_client import MCPClient

async def test_mcp_web_search():
    """直接测试MCP服务器的web_search工具"""
    print("🧪 直接测试MCP服务器的web_search工具...")
    
    client = None
    try:
        # 加载MCP配置
        config_path = os.path.join(os.path.abspath("."), 'config', 'llm_search_mcp_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            full_config = json.load(f)

        server_config = full_config["mcpServers"]["llm_search_mcp"]
        
        print(f"🔧 服务器配置: {server_config['command']} {server_config['args']}")
        print(f"🔑 环境变量: {list(server_config['env'].keys())}")

        # 初始化并连接MCP客户端
        client = MCPClient(server_config)
        await client.connect()
        
        print("✅ MCP客户端连接成功")

        # 列出可用工具
        tools = await client.list_tools()
        print(f"🔧 可用工具: {[tool['name'] for tool in tools]}")

        # 测试generate_search_queries工具
        print("\n🔧 步骤1: 测试generate_search_queries...")
        query_result = await client.call_tool("generate_search_queries", {
            "topic": "transformer attention mechanism",
            "description": "研究transformer模型中的注意力机制原理和优化方法"
        })
        
        print(f"📊 查询生成结果类型: {type(query_result)}")
        print(f"📄 查询生成结果键: {list(query_result.keys()) if isinstance(query_result, dict) else 'Not a dict'}")
        
        if isinstance(query_result, dict) and 'queries' in query_result:
            queries = query_result['queries'][:3]  # 只取前3个查询
            print(f"✅ 生成了 {len(queries)} 个查询:")
            for i, query in enumerate(queries):
                print(f"  {i+1}. {query}")
        else:
            print(f"❌ 查询生成结果格式异常: {query_result}")
            return

        # 测试web_search工具
        print("\n🔧 步骤2: 测试web_search...")
        search_result = await client.call_tool("web_search", {
            "queries": queries,
            "topic": "transformer attention mechanism"
        })
        
        print(f"📊 搜索结果类型: {type(search_result)}")
        print(f"📄 搜索结果键: {list(search_result.keys()) if isinstance(search_result, dict) else 'Not a dict'}")
        
        if isinstance(search_result, dict):
            urls = search_result.get('urls', [])
            print(f"🔢 搜索到 {len(urls)} 个URL")
            
            if urls:
                print("✅ 搜索成功，URL列表:")
                for i, url in enumerate(urls[:5]):
                    print(f"  {i+1}. {url}")
            else:
                print("❌ 没有搜索到URL")
                print(f"📄 完整搜索结果: {json.dumps(search_result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 搜索结果格式异常: {search_result}")

    except Exception as e:
        print(f"❌ MCP web_search测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if client:
            await client.disconnect()

async def test_mcp_step_by_step():
    """逐步测试MCP工具调用"""
    print("\n🧪 逐步测试MCP工具调用...")
    
    # 测试每个步骤都创建新的连接
    steps = [
        ("generate_search_queries", {
            "topic": "transformer attention mechanism",
            "description": "研究transformer模型中的注意力机制原理和优化方法"
        }),
    ]
    
    results = {}
    
    for step_name, step_args in steps:
        print(f"\n🔧 测试步骤: {step_name}")
        client = None
        try:
            # 加载配置
            config_path = os.path.join(os.path.abspath("."), 'config', 'llm_search_mcp_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)

            server_config = full_config["mcpServers"]["llm_search_mcp"]
            
            # 创建新连接
            client = MCPClient(server_config)
            await client.connect()
            
            # 调用工具
            result = await client.call_tool(step_name, step_args)
            results[step_name] = result
            
            print(f"✅ {step_name} 成功")
            print(f"📄 结果: {json.dumps(result, indent=2, ensure_ascii=False)[:200]}...")
            
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
            results[step_name] = None
        finally:
            if client:
                await client.disconnect()
    
    # 如果第一步成功，测试第二步
    if results.get("generate_search_queries"):
        queries = results["generate_search_queries"].get("queries", [])[:3]
        if queries:
            print(f"\n🔧 测试步骤: web_search (使用生成的查询)")
            client = None
            try:
                # 加载配置
                config_path = os.path.join(os.path.abspath("."), 'config', 'llm_search_mcp_config.json')
                with open(config_path, 'r', encoding='utf-8') as f:
                    full_config = json.load(f)

                server_config = full_config["mcpServers"]["llm_search_mcp"]
                
                # 创建新连接
                client = MCPClient(server_config)
                await client.connect()
                
                # 调用web_search
                result = await client.call_tool("web_search", {
                    "queries": queries,
                    "topic": "transformer attention mechanism"
                })
                
                print(f"✅ web_search 成功")
                print(f"📄 URL数量: {len(result.get('urls', []))}")
                
                if result.get('urls'):
                    print("🔗 前3个URL:")
                    for i, url in enumerate(result['urls'][:3]):
                        print(f"  {i+1}. {url}")
                else:
                    print("❌ 没有获取到URL")
                    print(f"📄 完整结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
            except Exception as e:
                print(f"❌ web_search 失败: {e}")
                import traceback
                traceback.print_exc()
            finally:
                if client:
                    await client.disconnect()

if __name__ == "__main__":
    print("🚀 开始MCP web_search测试")
    asyncio.run(test_mcp_web_search())
    asyncio.run(test_mcp_step_by_step())
    print("🏁 测试结束")
