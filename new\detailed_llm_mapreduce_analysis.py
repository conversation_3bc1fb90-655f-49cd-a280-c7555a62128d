#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析 LLMxMapReduce_V2 中爬虫数据处理流程和 JSON 格式转换
"""

import json
import re

def analyze_complete_data_flow():
    """分析完整的数据流转过程"""
    print("=== LLMxMapReduce_V2 完整数据流转分析 ===")
    print()
    
    print("## 1. 数据流转概览")
    print("```")
    print("URL列表 → 爬虫处理 → JSON输出 → Survey类加载 → 后续处理")
    print("```")
    print()
    
    # 详细的四阶段处理
    print("## 2. 四阶段数据处理详解")
    
    stages = [
        {
            "name": "Stage 1: URL 爬取 (_crawl_urls)",
            "input": "List[str] - URL列表",
            "process": "_crawl_and_collect() → _simple_crawl()",
            "output": "List[Dict] - 原始爬取数据",
            "data_structure": {
                "topic": "str - 研究主题",
                "url": "str - URL地址", 
                "raw_content": "str - 原始markdown内容",
                "error": "bool - 是否出错"
            }
        },
        {
            "name": "Stage 2: 内容过滤和标题生成 (_process_filter_and_titles)",
            "input": "原始爬取数据",
            "process": "_process_filter_and_title() + PAGE_REFINE_PROMPT",
            "output": "过滤后数据",
            "data_structure": {
                "topic": "str - 研究主题",
                "url": "str - URL地址",
                "raw_content": "str - 原始内容",
                "title": "str - LLM提取的标题",
                "filtered": "str - LLM过滤的内容",
                "error": "bool - 是否出错"
            }
        },
        {
            "name": "Stage 3: 相似度评分 (_process_similarity_scores)",
            "input": "过滤后数据",
            "process": "_process_similarity_score() + SIMILARITY_PROMPT",
            "output": "评分后数据",
            "data_structure": {
                "topic": "str - 研究主题",
                "url": "str - URL地址",
                "title": "str - 标题",
                "filtered": "str - 过滤后内容",
                "similarity": "int - LLM评分(0-100)",
                "error": "bool - 是否出错"
            }
        },
        {
            "name": "Stage 4: 结果处理和保存 (_process_results)",
            "input": "评分后数据",
            "process": "过滤 → 排序 → 格式化 → 保存JSON",
            "output": "JSONL文件",
            "data_structure": {
                "title": "str - 主题名称",
                "papers": "List[Dict] - 论文列表"
            }
        }
    ]
    
    for i, stage in enumerate(stages, 1):
        print(f"### {stage['name']}")
        print(f"- **输入**: {stage['input']}")
        print(f"- **处理**: {stage['process']}")
        print(f"- **输出**: {stage['output']}")
        print("- **数据结构**:")
        for key, desc in stage['data_structure'].items():
            print(f"  - `{key}`: {desc}")
        print()

def analyze_json_output_format():
    """分析最终JSON输出格式"""
    print("## 3. 最终JSON输出格式详解")
    print()
    
    print("### 3.1 JSONL文件结构")
    print("每行一个JSON对象，格式如下：")
    print()
    
    # 展示完整的JSON结构
    json_structure = {
        "title": "研究主题名称",
        "papers": [
            {
                "title": "论文标题（LLM提取）",
                "url": "原始URL地址",
                "txt": "过滤后的内容文本",
                "similarity": "相似度评分（0-100）"
            }
        ]
    }
    
    print("```json")
    print(json.dumps(json_structure, ensure_ascii=False, indent=2))
    print("```")
    print()
    
    print("### 3.2 关键字段说明")
    field_descriptions = {
        "title": "研究主题，来自用户输入的topic参数",
        "papers": "论文数组，包含所有通过过滤的文档",
        "papers[].title": "由LLM从网页内容中提取的标题",
        "papers[].url": "原始网页URL地址",
        "papers[].txt": "经过LLM过滤和精炼的内容文本",
        "papers[].similarity": "LLM评估的与主题相关性评分（0-100）"
    }
    
    for field, desc in field_descriptions.items():
        print(f"- **{field}**: {desc}")
    print()

def analyze_survey_class_processing():
    """分析Survey类如何处理JSON数据"""
    print("## 4. Survey类数据处理分析")
    print()
    
    print("### 4.1 Survey类初始化过程")
    print("```python")
    print("# 在 encode_pipeline.py 中")
    print("survey = Survey(json.loads(line))")
    print()
    print("# 在 Survey.__init__ 中")
    print("for paper in json_data['papers']:")
    print("    if paper.get('txt', ''):")
    print("        paper['bibkey'] = proc_title_to_str(paper['title'])")
    print("        self.papers[paper['bibkey']] = paper")
    print("```")
    print()
    
    print("### 4.2 数据转换过程")
    transformations = [
        {
            "step": "1. 基本字段映射",
            "process": "title → self.title, papers → 遍历处理"
        },
        {
            "step": "2. bibkey生成",
            "process": "proc_title_to_str(title) → 小写化、特殊字符处理、下划线替换"
        },
        {
            "step": "3. 论文过滤",
            "process": "只保留有txt内容的论文"
        },
        {
            "step": "4. 字典存储",
            "process": "以bibkey为键存储到self.papers字典"
        }
    ]
    
    for transform in transformations:
        print(f"**{transform['step']}**: {transform['process']}")
    print()
    
    print("### 4.3 bibkey生成规则")
    print("```python")
    print("def proc_title_to_str(origin_title):")
    print("    title = origin_title.lower().strip()      # 转小写，去空格")
    print("    title = title.replace('-', '_')           # 连字符转下划线")
    print("    title = re.sub(r'[^\\w\\s\\_]', '', title) # 移除特殊字符")
    print("    title = title.replace(' ', '_')           # 空格转下划线")
    print("    title = re.sub(r'_{2,}', '_', title)      # 多个下划线合并")
    print("    return title")
    print("```")
    print()

def analyze_data_compatibility():
    """分析数据兼容性"""
    print("## 5. 数据兼容性分析")
    print()
    
    print("### 5.1 LLMxMapReduce_V2 vs Survey_data 对比")
    print()
    
    # 对比表格
    comparison_data = [
        ("字段", "LLMxMapReduce_V2输出", "Survey_data期望", "兼容性"),
        ("---", "---", "---", "---"),
        ("title", "✓ 有", "✓ 需要", "✅ 完全兼容"),
        ("url", "✓ 有", "✓ 需要", "✅ 完全兼容"),
        ("txt", "✓ 有", "✓ 需要", "✅ 完全兼容"),
        ("similarity", "✓ 有", "❌ 无", "⚠️ 额外字段"),
        ("bibkey", "❌ 无", "✓ 需要", "⚠️ 需生成"),
        ("abstract", "❌ 无", "✓ 需要", "⚠️ 需提取"),
        ("txt_token", "❌ 无", "✓ 需要", "⚠️ 需计算"),
        ("authors", "❌ 无", "✓ 需要", "❌ 缺失"),
        ("latex", "❌ 无", "✓ 需要", "❌ 缺失"),
        ("其他元数据", "❌ 无", "✓ 需要", "❌ 缺失")
    ]
    
    for row in comparison_data:
        print(f"| {row[0]} | {row[1]} | {row[2]} | {row[3]} |")
    print()
    
    print("### 5.2 兼容性总结")
    print("- ✅ **基本兼容**: title, url, txt 三个核心字段完全匹配")
    print("- ⚠️ **需要扩展**: bibkey, abstract, txt_token 可以通过处理生成")
    print("- ❌ **结构差异**: 学术元数据（authors, latex等）无法从网页爬取获得")
    print("- 📊 **数据丰富度**: LLMxMapReduce_V2(4字段) vs Survey_data(25字段)")
    print()

def suggest_enhancement_solution():
    """建议增强解决方案"""
    print("## 6. 增强解决方案建议")
    print()
    
    print("### 6.1 扩展_process_results函数")
    print("```python")
    print("def _process_results(self, results, output_path, top_n=5, ...):")
    print("    processed_data = []")
    print("    for data in results:")
    print("        try:")
    print("            # 原有字段")
    print("            paper_data = {")
    print("                'title': data['title'],")
    print("                'url': data['url'],")
    print("                'txt': data['filtered'],")
    print("                'similarity': data.get('similarity', 0),")
    print("                # 新增兼容字段")
    print("                'bibkey': proc_title_to_str(data['title']),")
    print("                'abstract': data['filtered'][:500],  # 前500字符作为摘要")
    print("                'txt_token': estimate_tokens(data['filtered']),")
    print("                'txt_length': len(data['filtered']),")
    print("                'source_type': 'web_crawl',  # 标识数据来源")
    print("                'crawl_timestamp': time.time()  # 爬取时间戳")
    print("            }")
    print("            processed_data.append((data['topic'], paper_data))")
    print("```")
    print()
    
    print("### 6.2 需要添加的辅助函数")
    print("```python")
    print("def estimate_tokens(text: str) -> int:")
    print("    '''估算文本token数量'''")
    print("    return len(text.split()) * 1.3  # 粗略估算")
    print()
    print("def extract_abstract(text: str, max_length: int = 500) -> str:")
    print("    '''提取摘要'''")
    print("    # 可以使用更智能的摘要提取逻辑")
    print("    return text[:max_length].strip()")
    print("```")
    print()
    
    print("### 6.3 完整的兼容性改造")
    print("1. **保持向后兼容**: 原有4字段保持不变")
    print("2. **添加必需字段**: bibkey, abstract, txt_token")
    print("3. **添加元数据字段**: source_type, crawl_timestamp")
    print("4. **保持JSON格式**: 输出格式完全兼容现有系统")
    print()

def main():
    """主函数"""
    analyze_complete_data_flow()
    analyze_json_output_format()
    analyze_survey_class_processing()
    analyze_data_compatibility()
    suggest_enhancement_solution()

if __name__ == '__main__':
    main()
