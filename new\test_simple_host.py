#!/usr/bin/env python3
"""
简化的Host测试，专门测试MCP连接问题
"""

import asyncio
import logging
import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.llm_search_host import LLMSearchHost

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_host_search():
    """简单的Host搜索测试"""
    try:
        print("🔧 创建LLMSearchHost...")
        host = LLMSearchHost()
        
        print("🔍 执行简单搜索...")
        result = await host.search(
            topic="测试主题",
            description="简单的测试描述",
            top_n=3
        )
        
        print(f"✅ 搜索成功，结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"   结果字段: {list(result.keys())}")
        elif isinstance(result, list):
            print(f"   结果数量: {len(result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        logger.error(f"Search failed: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    print("🚀 开始简化的Host测试")
    print("=" * 50)
    
    success = await test_simple_host_search()
    
    print("=" * 50)
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        sys.exit(1)
