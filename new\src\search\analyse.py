import os
import json
import logging
import re
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from .llm_search_host import create_llm_search_host
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from request.wrapper import RequestWrapper
    
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AnalyseInterface:
    def __init__(self,
                 base_dir: str = "new/test",
                 max_interaction_rounds: int = 3,
                 config_path: Optional[str] = None,
                 llm_model: str = "gemini-2.0-flash-thinking-exp-01-21",
                 llm_infer_type: str = "OpenAI"):
 
        self.base_dir = Path(base_dir)
        self.max_interaction_rounds = max_interaction_rounds
        self.config_path = config_path
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.config_dir = self.base_dir / "config"
        self.config_dir.mkdir(exist_ok=True)
        self.llm_model = llm_model
        self.llm_infer_type = llm_infer_type
        self._init_llm_components()
        self._init_llm_search()

        self._load_config()
        self._load_prompt_templates()

        logger.info(f"AnalyseInterface initialized:")
        logger.info(f"  - Base directory: {self.base_dir}")
        logger.info(f"  - Max interaction rounds: {self.max_interaction_rounds}")
        logger.info(f"  - LLM model: {self.llm_model}")
        logger.info(f"  - LLM search engine: {self.llm_search.engine if hasattr(self.llm_search, 'engine') else 'MCP'}")

    def _init_llm_components(self):
        try:
            self.llm_wrapper = RequestWrapper(
                model=self.llm_model,
                infer_type=self.llm_infer_type,
                use_memory=True,
                max_context_messages=10
            )
            logger.info("LLM wrapper initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM wrapper: {e}")
            raise

    def _init_llm_search(self):
        try:
            self.llm_search = create_llm_search_host(
                model=self.llm_model,
                infer_type=self.llm_infer_type,
                max_workers=10
            )
            logger.info("LLM search host initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM search host: {e}")
            raise

    async def analyse(self, topic: str, description: Optional[str] = None,
                      top_n: int = 20, **kwargs) -> List[Dict[str, Any]]:
        """
        执行完整的文献分析工作流

        Args:
            topic: 研究主题
            description: 主题描述（可选）
            top_n: 目标文献数量
            **kwargs: 其他参数

        Returns:
            List[Dict[str, Any]]: 文献检索结果列表（已在llm_search_server中保存）
        """
        logger.info(f"Starting comprehensive literature analysis for topic: '{topic}'")

        try:
            logger.info("=== Phase 1: Topic Expansion with LLM ===")
            expanded_topic = self._expand_topic_with_llm(topic, description)

            print("expanded_topic:", expanded_topic, "\n")

            logger.info(f"=== Phase 2: Interactive Refinement ({self.max_interaction_rounds} rounds) ===")
            refined_topic = self._interactive_refinement(expanded_topic)

            logger.info("=== Phase 3: Literature Retrieval using LLM Search Host ===")
            literature_results = await self._retrieve_literature(refined_topic, top_n)

            logger.info(f"✅ Analysis completed successfully. Retrieved {len(literature_results)} papers")
            logger.info("📁 Results have been saved by llm_search_server during crawling process")
            return literature_results

        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            raise
    
    def _load_config(self):
        try:
            if self.config_path and os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "search": {
                        "default_top_n": 20,
                        "default_engine": "google",
                        "similarity_threshold": 80
                    },
                    "interaction": {
                        "timeout_seconds": 0,  # 无超时限制
                        "auto_continue": False
                    }
                }
            logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load config, using defaults: {e}")
            self.config = {}

    def _load_prompt_templates(self):
        self.prompts = {
            "topic_expansion": """
你是一个专业的学术研究分析专家。请对以下研究主题进行深度分析和多角度进行扩写。

原始主题：{topic}
原始主题描述：{description}

请从多个角度进行分析，你将生成一段提示词传递给搜索智能体，他将根据你的提示词生成一些列搜索引擎查询语句，所以请确保你的描述专业且全面。请直接以JSON格式给出你生成的描述。

{{
    "description": "你生成的描述"
}}
""",
            "refinement": """
基于用户反馈，请优化以下研究分析：

当前描述：
{current_analysis}

用户反馈：
{user_feedback}

请根据反馈调整和改进分析内容，保持JSON格式输出。
""",

        }

    def _expand_topic_with_llm(self, topic: str, description: Optional[str] = None) -> Dict[str, Any]:
        logger.info(f"Expanding topic with LLM: '{topic}'")

        prompt = self.prompts["topic_expansion"].format(
            topic=topic,
            description=description or "无额外描述"
        )

        response = self.llm_wrapper.completion(prompt)

        expanded_topic = self._parse_json_response(response)

        # 添加原始主题信息以便后续使用
        if expanded_topic:
            expanded_topic['original_topic'] = topic
            expanded_topic['original_description'] = description
        else:
            # 如果解析失败，创建基本结构
            expanded_topic = {
                "description": description or f"Research on {topic}",
                "original_topic": topic,
                "original_description": description
            }

        logger.info("Topic expansion completed successfully")
        return expanded_topic

    def _parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        try:
            if response.strip().startswith('{'):
                return json.loads(response.strip())

            import re
            json_pattern = r'```json\s*(.*?)\s*```'
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(1).strip())

            brace_pattern = r'\{.*\}'
            match = re.search(brace_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group(0))

            return None

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            return None

    def _create_task_directory(self, task: str) -> Path:

        safe_task_name = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_task_name = safe_task_name.replace(' ', '_')

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        task_dir_name = f"{safe_task_name}_{timestamp}"

        task_dir = self.base_dir / task_dir_name
        task_dir.mkdir(parents=True, exist_ok=True)
        (task_dir / "papers").mkdir(exist_ok=True)
        (task_dir / "analysis").mkdir(exist_ok=True)
        (task_dir / "logs").mkdir(exist_ok=True)

        return task_dir
    
    def _interactive_refinement(self, expanded_topic: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Starting interactive refinement (max {self.max_interaction_rounds} rounds)")

        current_analysis = expanded_topic.copy()

        for round_num in range(self.max_interaction_rounds):
            logger.info(f"--- Interaction Round {round_num + 1}/{self.max_interaction_rounds} ---")

            self._display_analysis(current_analysis, round_num + 1)

            user_feedback = self._get_user_feedback(round_num + 1)

            if user_feedback.get('satisfied', False):
                logger.info("User satisfied with current analysis, proceeding to literature search")
                break

            if user_feedback.get('feedback_text'):
                logger.info("Refining analysis based on user feedback")
                current_analysis = self._refine_with_feedback(current_analysis, user_feedback)
            else:
                logger.info("No specific feedback provided, keeping current analysis")

        logger.info("Interactive refinement completed")
        return current_analysis

    def _display_analysis(self, analysis: Dict[str, Any], round_num: int):
        """展示当前分析结果给用户"""
        print(f"\n{'='*60}")
        print(f"第 {round_num} 轮分析结果")
        print(f"{'='*60}")
        print(f"\n🎯 我对这个topic的分析结果如下：{analysis.get('description', 'N/A')}")
        

    def _get_user_feedback(self, round_num: int) -> Dict[str, Any]:
        print(f"\n{'='*60}")
        print(f"请提供第 {round_num} 轮反馈")
        print(f"{'='*60}")

        try:
            satisfied_input = input("\n您是否满意当前的分析结果？(y/n/回车继续): ").strip().lower()

            if satisfied_input in ['y', 'yes', '是', 'ok']:
                return {'satisfied': True}

            if satisfied_input in ['', 'continue', '继续']:
                if round_num >= self.max_interaction_rounds:
                    return {'satisfied': True}
                else:
                    return {'satisfied': False}

            # 获取具体反馈
            print("\n请提供具体的改进建议")
            # print("- 需要调整的概念定义")
            # print("- 需要添加或删除的子领域")
            # print("- 需要补充的研究问题")
            # print("- 其他任何改进意见")
            # print("\n输入您的反馈（回车结束）：")

            feedback_text = input().strip()

            return {
                'satisfied': False,
                'feedback_text': feedback_text,
                'round': round_num
            }

        except KeyboardInterrupt:
            logger.info("User interrupted, proceeding with current analysis")
            return {'satisfied': True}
        except Exception as e:
            logger.warning(f"Error getting user feedback: {e}")
            return {'satisfied': True}

    def _refine_with_feedback(self, current_analysis: Dict[str, Any],
                            user_feedback: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("Refining analysis based on user feedback")

        prompt = self.prompts["refinement"].format(
            current_analysis=json.dumps(current_analysis, ensure_ascii=False, indent=2),
            user_feedback=user_feedback.get('feedback_text', '')
        )

        response = self.llm_wrapper.completion(prompt)
        refined_analysis = self._parse_json_response(response)

        if refined_analysis:
            logger.info("Analysis refined successfully")
            return refined_analysis
        else:
            logger.warning("Failed to parse refined analysis, keeping original")
            return current_analysis

    async def _retrieve_literature(self, refined_topic: Dict[str, Any], top_n: int = 20) -> List[Dict[str, Any]]:
        logger.info("Starting literature retrieval using LLM Search Host")

        topic_description = refined_topic.get('description', '')

        # 从描述中提取核心概念
        core_concept = refined_topic.get('core_concept')
        if not core_concept:
            if '：' in topic_description:
                core_concept = topic_description.split('：')[0].strip()
            elif ':' in topic_description:
                core_concept = topic_description.split(':')[0].strip()
            else:
                core_concept = refined_topic.get('original_topic', 'research')

        logger.info(f"Delegating to LLM Search Host for topic: '{core_concept}'")
        logger.info(f"Topic description length: {len(topic_description)} characters")

        search_result = await self.llm_search.search(
            topic=core_concept,
            description=topic_description,
            top_n=top_n,
            similarity_threshold=self.config.get('search', {}).get('similarity_threshold', 80),
            min_length=350,
            max_length=20000
        )

        # 处理Host返回的结果格式
        if isinstance(search_result, dict):
            # 如果返回的是字典格式（包含查询生成等信息）
            logger.info(f"Host returned structured result with keys: {list(search_result.keys())}")

            # 检查是否有实际的文献结果
            if 'results' in search_result and isinstance(search_result['results'], list):
                literature_results = search_result['results']
                logger.info(f"Successfully retrieved {len(literature_results)} literature papers from Host")
            elif 'queries' in search_result:
                # 如果只有查询生成结果，创建一个包含查询信息的结果
                query_count = search_result.get('query_count', 0)
                logger.info(f"Host generated {query_count} search queries, but no literature results yet")
                # 返回一个包含查询信息的结果，表示搜索已启动
                literature_results = [{
                    'type': 'search_initiated',
                    'topic': core_concept,
                    'description': topic_description,
                    'queries_generated': query_count,
                    'status': 'queries_generated',
                    'message': f'Generated {query_count} search queries for topic: {core_concept}'
                }]
            else:
                logger.warning("Host returned unexpected result format")
                literature_results = []
        elif isinstance(search_result, list):
            # 如果返回的是列表格式（期望的文献列表）
            literature_results = search_result
            logger.info(f"Successfully retrieved {len(literature_results)} literature papers from Host")
        else:
            logger.warning(f"Host returned unexpected result type: {type(search_result)}")
            literature_results = []

        return literature_results

async def analyse(task: str, description: Optional[str] = None, top_n: int = 20,
                  max_interaction_rounds: int = 3) -> List[Dict[str, Any]]:
    analyser = AnalyseInterface(max_interaction_rounds=max_interaction_rounds)
    return await analyser.analyse(task, description, top_n)


if __name__ == "__main__":
    import sys

    topic = sys.argv[1]
    description = sys.argv[2] if len(sys.argv) > 2 else None
    top_n = int(sys.argv[3]) if len(sys.argv) > 3 else 20

    print(f"🔍 开始分析主题: {topic}")
    if description:
        print(f"📝 描述: {description}")
    print(f"🎯 目标文献数量: {top_n}")
    print("-" * 50)

    try:
        import asyncio
        literature_results = asyncio.run(analyse(topic, description, top_n))
        print(f"\n✅ 分析完成！检索到 {len(literature_results)} 篇文献")
        print("📁 文献已保存在 llm_search_server 指定的目录中")
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
