#!/usr/bin/env python3
"""
对话历史功能使用示例
演示如何在LLM Search中使用对话历史存储功能
"""

import sys
import os
import json
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def example_basic_memory():
    """基本对话历史使用示例"""
    print("📝 示例1: 基本对话历史功能")
    print("-" * 50)
    
    try:
        from src.search.llm_search_host import LLM_search
        
        # 创建启用内存的LLM_search实例
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            use_memory=True,
            max_context_messages=8
        )
        
        print("✓ 创建LLM_search实例（启用内存）")
        
        # 模拟一系列相关的搜索查询
        topics = [
            "机器学习基础",
            "深度学习神经网络",
            "卷积神经网络CNN",
            "循环神经网络RNN"
        ]
        
        print(f"\n准备进行 {len(topics)} 个相关主题的搜索...")
        
        for i, topic in enumerate(topics, 1):
            print(f"\n🔍 搜索 {i}: {topic}")
            
            try:
                # 生成搜索查询（这会使用LLM，并保存对话历史）
                queries = llm_search.get_queries(
                    topic=topic,
                    description=f"关于{topic}的详细信息和学习资源"
                )
                
                print(f"  ✓ 生成了 {len(queries)} 个查询")
                if queries:
                    print(f"  示例查询: {queries[0]}")
                
                # 检查对话历史
                history = llm_search.get_conversation_history(limit=1)
                if history:
                    latest = history[-1]
                    print(f"  ✓ 最新对话已保存 (时间: {latest['timestamp'][:19]})")
                
            except Exception as e:
                print(f"  ❌ 搜索失败: {e}")
        
        # 显示完整的对话历史统计
        stats = llm_search.get_memory_statistics()
        print(f"\n📊 对话历史统计:")
        for model, model_stats in stats.items():
            print(f"  模型: {model}")
            print(f"    总对话数: {model_stats['total_conversations']}")
            print(f"    首次对话: {model_stats['first_conversation'][:19] if model_stats['first_conversation'] else 'N/A'}")
            print(f"    最新对话: {model_stats['last_conversation'][:19] if model_stats['last_conversation'] else 'N/A'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本示例失败: {e}")
        return False

def example_context_continuity():
    """上下文连续性示例"""
    print("\n🔗 示例2: 上下文连续性")
    print("-" * 50)
    
    try:
        from src.search.llm_search_host import LLM_search
        
        # 创建新的实例来测试上下文加载
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            use_memory=True,
            max_context_messages=6
        )
        
        print("✓ 创建新的LLM_search实例")
        
        # 获取历史对话
        history = llm_search.get_conversation_history()
        print(f"✓ 加载了 {len(history)} 条历史对话")
        
        if history:
            print("\n最近的对话:")
            for i, conv in enumerate(history[-3:], 1):  # 显示最后3条
                user_msg = conv['messages'][0]['content'] if conv['messages'] else "N/A"
                print(f"  {i}. 用户: {user_msg[:50]}...")
                print(f"     助手: {conv['response'][:50]}...")
                print(f"     时间: {conv['timestamp'][:19]}")
        
        # 测试上下文感知的查询
        print(f"\n🧠 测试上下文感知查询...")
        
        # 这个查询应该能够理解之前关于机器学习的讨论
        contextual_topic = "Transformer架构"
        print(f"查询主题: {contextual_topic}")
        
        try:
            queries = llm_search.get_queries(
                topic=contextual_topic,
                description="基于之前讨论的机器学习背景，深入了解Transformer架构"
            )
            
            print(f"✓ 生成了 {len(queries)} 个上下文感知的查询")
            if queries:
                print(f"  示例: {queries[0]}")
            
        except Exception as e:
            print(f"❌ 上下文查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文示例失败: {e}")
        return False

def example_memory_management():
    """内存管理示例"""
    print("\n🗂️ 示例3: 内存管理功能")
    print("-" * 50)
    
    try:
        from src.search.llm_search_host import LLM_search
        
        llm_search = LLM_search(use_memory=True)
        
        print("✓ 创建LLM_search实例")
        
        # 导出对话历史
        export_file = "conversation_export.json"
        success = llm_search.export_conversation_history(export_file)
        
        if success and os.path.exists(export_file):
            print(f"✓ 对话历史已导出到: {export_file}")
            
            # 显示导出文件的大小
            file_size = os.path.getsize(export_file)
            print(f"  文件大小: {file_size} 字节")
            
            # 读取并显示部分内容
            with open(export_file, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            print(f"  导出了 {len(exported_data)} 条对话记录")
            
        else:
            print("⚠️  导出失败或文件不存在")
        
        # 测试内存开关
        print(f"\n🔄 测试内存功能开关...")
        
        # 禁用内存
        llm_search.set_memory_enabled(False)
        print("✓ 内存功能已禁用")
        
        # 启用内存
        llm_search.set_memory_enabled(True)
        print("✓ 内存功能已重新启用")
        
        # 清理导出文件
        if os.path.exists(export_file):
            os.remove(export_file)
            print(f"✓ 清理了导出文件: {export_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存管理示例失败: {e}")
        return False

def example_multi_model_memory():
    """多模型内存分离示例"""
    print("\n🔀 示例4: 多模型内存分离")
    print("-" * 50)
    
    try:
        from src.search.llm_search_host import LLM_search
        from src.utils.memory_manager import get_memory_manager
        
        # 创建不同模型的实例
        models = [
            "gemini-2.0-flash-thinking-exp-01-21",
            "gpt-4",
            "claude-3-sonnet"
        ]
        
        memory_manager = get_memory_manager()
        
        print(f"✓ 准备测试 {len(models)} 个不同模型")
        
        # 为每个模型添加测试对话
        for model in models:
            memory_manager.add_conversation(
                model=model,
                messages=[{"role": "user", "content": f"你好，我正在测试{model}模型"}],
                response=f"你好！我是{model}，很高兴为您服务。",
                metadata={"test_type": "multi_model", "model": model}
            )
            print(f"  ✓ 为 {model} 添加了测试对话")
        
        # 检查每个模型的独立历史
        print(f"\n📊 各模型的对话历史:")
        all_models = memory_manager.get_all_models()
        
        for model in all_models:
            history = memory_manager.get_conversation_history(model)
            print(f"  {model}: {len(history)} 条对话")
            
            if history:
                latest = history[-1]
                print(f"    最新: {latest['response'][:40]}...")
        
        # 验证模型间的隔离性
        print(f"\n🔒 验证模型间隔离性:")
        
        for model in models[:2]:  # 只测试前两个
            model_history = memory_manager.get_conversation_history(model)
            other_models_content = []
            
            for other_model in all_models:
                if other_model != model:
                    other_history = memory_manager.get_conversation_history(other_model)
                    other_models_content.extend([conv['response'] for conv in other_history])
            
            # 检查当前模型的历史中是否包含其他模型的内容
            model_content = [conv['response'] for conv in model_history]
            
            isolated = not any(content in other_models_content for content in model_content)
            status = "✓ 隔离正常" if isolated else "❌ 发现泄露"
            print(f"  {model}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多模型示例失败: {e}")
        return False

def show_memory_file_content():
    """显示内存文件内容"""
    print("\n📄 内存文件内容预览")
    print("-" * 50)
    
    memory_file = "memory.json"
    
    if os.path.exists(memory_file):
        try:
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            print(f"✓ 内存文件: {memory_file}")
            print(f"✓ 文件大小: {os.path.getsize(memory_file)} 字节")
            print(f"✓ 包含模型: {len(memory_data)} 个")
            
            for model, conversations in memory_data.items():
                print(f"\n模型: {model}")
                print(f"  对话数量: {len(conversations)}")
                
                if conversations:
                    first_conv = conversations[0]
                    last_conv = conversations[-1]
                    
                    print(f"  首次对话: {first_conv['timestamp'][:19]}")
                    print(f"  最新对话: {last_conv['timestamp'][:19]}")
                    
                    # 显示最新对话的简要内容
                    if last_conv['messages']:
                        user_msg = last_conv['messages'][0]['content']
                        print(f"  最新用户消息: {user_msg[:60]}...")
                    
                    print(f"  最新助手回复: {last_conv['response'][:60]}...")
        
        except Exception as e:
            print(f"❌ 读取内存文件失败: {e}")
    
    else:
        print("⚠️  内存文件不存在")

def main():
    """主函数"""
    print("🚀 对话历史功能使用示例")
    print("=" * 60)
    
    examples = [
        ("基本功能", example_basic_memory),
        ("上下文连续性", example_context_continuity),
        ("内存管理", example_memory_management),
        ("多模型分离", example_multi_model_memory)
    ]
    
    results = []
    
    for name, example_func in examples:
        try:
            result = example_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ 示例 '{name}' 发生异常: {e}")
            results.append((name, False))
    
    # 显示内存文件内容
    show_memory_file_content()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 示例运行总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个示例成功")
    
    if passed == total:
        print("🎉 所有示例运行成功！对话历史功能正常工作。")
    else:
        print("⚠️  部分示例失败，请检查配置和实现。")
    
    print(f"\n💡 提示:")
    print(f"  • 对话历史保存在: memory.json")
    print(f"  • 可以通过 get_conversation_history() 查看历史")
    print(f"  • 可以通过 clear_conversation_history() 清除历史")
    print(f"  • 可以通过 set_memory_enabled() 控制功能开关")

if __name__ == "__main__":
    main()
