#!/usr/bin/env python3
"""
简化的多轮工具选择测试
测试LLM是否能够自主选择多个工具并判断结束
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.llm_search_host import LLMSearchHost

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_multi_turn_simple():
    """简化的多轮工具选择测试"""
    logger.info("🎯 开始简化多轮工具选择测试")
    logger.info("目标: 验证LLM能否自主选择多个工具")
    logger.info("="*80)
    
    try:
        # 初始化LLMSearchHost
        logger.info("🔧 初始化LLMSearchHost...")
        host = LLMSearchHost()
        logger.info("✅ LLMSearchHost初始化成功")
        
        # 简单的测试参数
        topic = "机器学习"
        description = "机器学习的基本概念"
        top_n = 2
        
        logger.info("📋 测试参数:")
        logger.info(f"   主题: {topic}")
        logger.info(f"   描述: {description}")
        logger.info(f"   目标文献数: {top_n}")
        logger.info("="*80)
        
        # 执行搜索
        logger.info("🎯 开始执行多轮工具选择...")
        result = await host.search(
            topic=topic,
            description=description,
            top_n=top_n,
            similarity_threshold=0.7,
            min_length=100,
            max_length=2000
        )
        
        logger.info("="*80)
        logger.info("📊 搜索结果:")
        logger.info(f"   结果类型: {type(result)}")
        
        if isinstance(result, dict):
            logger.info(f"   结果字段: {list(result.keys())}")
            if 'queries' in result:
                logger.info(f"   生成查询数: {len(result.get('queries', []))}")
            if 'search_results' in result:
                logger.info(f"   搜索结果数: {len(result.get('search_results', []))}")
            if 'processing_metadata' in result:
                metadata = result['processing_metadata']
                logger.info(f"   使用的工具: {metadata.get('tools_used', [])}")
                logger.info(f"   对话轮数: {metadata.get('turns', 0)}")
        elif isinstance(result, list):
            logger.info(f"   结果数量: {len(result)}")
            if result:
                logger.info(f"   第一个结果类型: {type(result[0])}")
                if isinstance(result[0], dict):
                    logger.info(f"   第一个结果字段: {list(result[0].keys())}")
        
        logger.info("="*80)
        logger.info("✅ 简化多轮工具选择测试完成!")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    result = asyncio.run(test_multi_turn_simple())
    if result:
        print("🎉 测试成功!")
    else:
        print("❌ 测试失败!")
