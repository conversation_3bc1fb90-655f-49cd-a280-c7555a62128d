#!/usr/bin/env python3
"""
单个工具测试 - 逐个测试每个MCP工具
确保每个工具都能正常工作并返回结果
"""

import asyncio
import logging
import sys
import os
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.llm_search_mcp_client import MCPClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_single_tools():
    """逐个测试每个MCP工具"""
    logger.info("🎯 开始单个工具测试")
    logger.info("目标: 逐个测试每个MCP工具的功能")
    logger.info("="*80)
    
    try:
        # 初始化MCP客户端
        logger.info("🔧 初始化MCP客户端...")
        server_config = {
            "command": "python",
            "args": ["src/search/llm_search_mcp_server.py"],
            "cwd": "."
        }
        client = MCPClient(server_config)
        await client.connect()
        logger.info("✅ MCP客户端连接成功")
        
        # 获取可用工具
        tools = await client.list_tools()
        logger.info(f"📋 可用工具: {[tool['name'] for tool in tools]}")
        
        # 测试参数
        topic = "深度学习"
        description = "深度学习的基本原理"
        
        logger.info("="*80)
        logger.info("🧪 开始逐个测试工具...")
        
        # 1. 测试 generate_search_queries
        logger.info("🔧 测试工具 1: generate_search_queries")
        try:
            result1 = await client.call_tool("generate_search_queries", {
                "topic": topic,
                "description": description
            })
            logger.info(f"✅ generate_search_queries 成功")
            logger.info(f"   结果类型: {type(result1)}")
            if isinstance(result1, dict):
                logger.info(f"   结果字段: {list(result1.keys())}")
                queries = result1.get('queries', [])
                logger.info(f"   生成查询数: {len(queries)}")
                if queries:
                    logger.info(f"   前3个查询: {queries[:3]}")
        except Exception as e:
            logger.error(f"❌ generate_search_queries 失败: {e}")
            result1 = None
        
        # 2. 测试 web_search (如果第1步成功)
        logger.info("🔧 测试工具 2: web_search")
        if result1 and isinstance(result1, dict) and result1.get('queries'):
            try:
                queries = result1['queries'][:2]  # 只用前2个查询
                result2 = await client.call_tool("web_search", {
                    "queries": queries,
                    "topic": topic
                })
                logger.info(f"✅ web_search 成功")
                logger.info(f"   结果类型: {type(result2)}")
                if isinstance(result2, dict):
                    logger.info(f"   结果字段: {list(result2.keys())}")
                    urls = result2.get('urls', [])
                    logger.info(f"   搜索到URL数: {len(urls)}")
                    if urls:
                        logger.info(f"   前3个URL: {urls[:3]}")
            except Exception as e:
                logger.error(f"❌ web_search 失败: {e}")
                result2 = None
        else:
            logger.warning("⚠️ 跳过 web_search (generate_search_queries 未成功)")
            result2 = None
        
        # 3. 测试 analyze_search_results (如果第2步成功)
        logger.info("🔧 测试工具 3: analyze_search_results")
        if result2 and isinstance(result2, dict) and result2.get('urls'):
            try:
                result3 = await client.call_tool("analyze_search_results", {
                    "search_results": result2.get('urls', []),
                    "topic": topic
                })
                logger.info(f"✅ analyze_search_results 成功")
                logger.info(f"   结果类型: {type(result3)}")
                if isinstance(result3, dict):
                    logger.info(f"   结果字段: {list(result3.keys())}")
            except Exception as e:
                logger.error(f"❌ analyze_search_results 失败: {e}")
                result3 = None
        else:
            logger.warning("⚠️ 跳过 analyze_search_results (web_search 未成功)")
            result3 = None
        
        # 4. 测试 crawl_urls (如果有URL)
        logger.info("🔧 测试工具 4: crawl_urls")
        if result2 and isinstance(result2, dict) and result2.get('urls'):
            try:
                urls = result2['urls'][:2]  # 只爬取前2个URL
                result4 = await client.call_tool("crawl_urls", {
                    "urls": urls,
                    "topic": topic
                })
                logger.info(f"✅ crawl_urls 成功")
                logger.info(f"   结果类型: {type(result4)}")
                if isinstance(result4, dict):
                    logger.info(f"   结果字段: {list(result4.keys())}")
                    crawled_data = result4.get('crawled_data', [])
                    logger.info(f"   爬取内容数: {len(crawled_data)}")
                    if crawled_data:
                        first_content = crawled_data[0]
                        if isinstance(first_content, dict):
                            logger.info(f"   第一个内容字段: {list(first_content.keys())}")
                            if 'content' in first_content:
                                content_preview = first_content['content'][:200]
                                logger.info(f"   内容预览: {content_preview}...")
            except Exception as e:
                logger.error(f"❌ crawl_urls 失败: {e}")
                result4 = None
        else:
            logger.warning("⚠️ 跳过 crawl_urls (web_search 未成功)")
            result4 = None
        
        # 断开连接
        await client.disconnect()
        logger.info("🔌 MCP客户端已断开连接")
        
        logger.info("="*80)
        logger.info("📊 测试总结:")
        logger.info(f"   generate_search_queries: {'✅' if result1 else '❌'}")
        logger.info(f"   web_search: {'✅' if result2 else '❌'}")
        logger.info(f"   analyze_search_results: {'✅' if result3 else '❌'}")
        logger.info(f"   crawl_urls: {'✅' if result4 else '❌'}")
        
        # 如果有成功的结果，保存到文件
        if any([result1, result2, result3, result4]):
            output_dir = Path("test_single_tools_output")
            output_dir.mkdir(exist_ok=True)
            
            results = {
                "generate_search_queries": result1,
                "web_search": result2,
                "analyze_search_results": result3,
                "crawl_urls": result4
            }
            
            output_file = output_dir / "tool_test_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📁 结果已保存到: {output_file}")
        
        logger.info("✅ 单个工具测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_single_tools())
    if success:
        print("🎉 单个工具测试成功!")
    else:
        print("❌ 单个工具测试失败!")
