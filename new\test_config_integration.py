#!/usr/bin/env python3
"""
测试配置集成 - 验证Host和Server都从model_config.json读取配置
"""

import json
import os
import sys

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def test_model_config_loading():
    """测试模型配置加载"""
    print("=== 测试模型配置加载 ===")
    
    config_path = "config/model_config.json"
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✓ 配置文件加载成功: {config_path}")
        
        # 检查搜索配置
        if 'search' in config:
            search_config = config['search']
            print("✓ 搜索配置存在")
            
            # 检查各个搜索组件的配置
            components = [
                'query_generation', 'content_analysis', 'similarity_scoring',
                'page_refine', 'host_llm'
            ]
            
            for component in components:
                if component in search_config:
                    comp_config = search_config[component]
                    model = comp_config.get('model', 'N/A')
                    infer_type = comp_config.get('infer_type', 'N/A')
                    print(f"  ✓ {component}: {model} ({infer_type})")
                else:
                    print(f"  ✗ {component}: 配置缺失")
                    return False
        else:
            print("✗ 搜索配置缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def test_host_config_integration():
    """测试Host配置集成"""
    print("\n=== 测试Host配置集成 ===")
    
    try:
        from src.search.llm_search_host import LLM_search, MODEL_CONFIG
        
        print("✓ Host模块导入成功")
        
        # 检查配置是否正确加载
        if 'search' in MODEL_CONFIG:
            search_config = MODEL_CONFIG['search']
            host_config = search_config.get('host_llm', {})
            
            print(f"✓ Host配置加载: {host_config}")
            
            # 测试默认初始化
            llm_search = LLM_search()
            print(f"✓ Host默认初始化成功")
            print(f"  模型: {llm_search.model}")
            print(f"  推理类型: {llm_search.infer_type}")
            
            # 测试指定参数初始化
            llm_search_custom = LLM_search(model="custom-model", infer_type="Custom")
            print(f"✓ Host自定义初始化成功")
            print(f"  模型: {llm_search_custom.model}")
            print(f"  推理类型: {llm_search_custom.infer_type}")
            
            return True
        else:
            print("✗ Host配置中缺少搜索配置")
            return False
        
    except Exception as e:
        print(f"✗ Host配置集成测试失败: {e}")
        return False

def test_server_config_integration():
    """测试Server配置集成"""
    print("\n=== 测试Server配置集成 ===")
    
    try:
        # 导入Server模块（不启动Server）
        import sys
        sys.path.append('src/search')
        
        # 直接测试配置加载函数
        from src.search.llm_search_mcp_server import load_server_config
        
        server_config = load_server_config()
        print("✓ Server配置加载成功")
        
        # 检查关键配置项
        required_keys = [
            'default_model', 'default_infer_type', 'content_analysis_model',
            'similarity_model', 'page_refine_model', 'default_engine',
            'default_top_n', 'default_similarity_threshold'
        ]
        
        for key in required_keys:
            if key in server_config:
                value = server_config[key]
                print(f"  ✓ {key}: {value}")
            else:
                print(f"  ✗ {key}: 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Server配置集成测试失败: {e}")
        return False

def test_config_consistency():
    """测试配置一致性"""
    print("\n=== 测试配置一致性 ===")
    
    try:
        # 加载原始配置
        with open("config/model_config.json", 'r', encoding='utf-8') as f:
            original_config = json.load(f)
        
        # 测试Host配置
        from src.search.llm_search_host import MODEL_CONFIG as host_config
        
        # 测试Server配置
        from src.search.llm_search_mcp_server import load_server_config
        server_config = load_server_config()
        
        # 检查Host和原始配置的一致性
        original_host = original_config['search']['host_llm']
        host_loaded = host_config['search']['host_llm']
        
        if original_host == host_loaded:
            print("✓ Host配置与原始配置一致")
        else:
            print("✗ Host配置与原始配置不一致")
            print(f"  原始: {original_host}")
            print(f"  加载: {host_loaded}")
            return False
        
        # 检查Server配置是否正确映射
        original_query_gen = original_config['search']['query_generation']
        server_default = {
            'model': server_config['default_model'],
            'infer_type': server_config['default_infer_type']
        }
        
        if original_query_gen == server_default:
            print("✓ Server配置与原始配置一致")
        else:
            print("✗ Server配置与原始配置不一致")
            print(f"  原始查询生成: {original_query_gen}")
            print(f"  Server默认: {server_default}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置一致性测试失败: {e}")
        return False

def print_config_summary():
    """打印配置总结"""
    print("\n" + "="*60)
    print("配置集成总结")
    print("="*60)
    print("""
🎯 配置集成要点:

✅ 统一配置源
   - 所有模型配置都来自 config/model_config.json
   - Host和Server都从同一配置文件读取
   - 保持配置格式一致性

✅ Host配置
   - 从 search.host_llm 读取默认模型配置
   - 支持参数覆盖（向后兼容）
   - 初始化时显示使用的模型信息

✅ Server配置
   - 从 search 各组件读取专用模型配置
   - query_generation → default_model
   - content_analysis → content_analysis_model
   - similarity_scoring → similarity_model
   - page_refine → page_refine_model

✅ 配置一致性
   - Host和Server使用相同的配置源
   - 配置加载错误时使用合理默认值
   - 配置更改只需修改一个文件

📝 配置文件结构:
config/
├── model_config.json (统一模型配置)
└── llm_search_mcp_config.json (MCP服务配置)
    """)

def main():
    """主测试函数"""
    print("开始测试配置集成...")
    print("验证Host和Server都从model_config.json读取配置")
    
    # 执行所有测试
    results = {}
    
    print("\n" + "="*60)
    print("执行配置集成测试")
    print("="*60)
    
    # 测试1: 模型配置加载
    results['模型配置加载'] = test_model_config_loading()
    
    # 测试2: Host配置集成
    results['Host配置集成'] = test_host_config_integration()
    
    # 测试3: Server配置集成
    results['Server配置集成'] = test_server_config_integration()
    
    # 测试4: 配置一致性
    results['配置一致性'] = test_config_consistency()
    
    # 打印配置总结
    print_config_summary()
    
    # 总结测试结果
    print("\n" + "="*60)
    print("配置集成测试结果")
    print("="*60)
    
    all_passed = all(results.values())
    
    if all_passed:
        print("🎉 所有配置集成测试通过！")
        print("\n✅ 测试结果:")
        for test_name, result in results.items():
            print(f"  {test_name}: {'✓' if result else '✗'}")
        
        print("\n🎯 配置集成完成:")
        print("• Host和Server都从model_config.json读取配置")
        print("• 配置格式统一，支持组件级别的模型配置")
        print("• 保持向后兼容性，支持参数覆盖")
        print("• 配置加载失败时使用合理默认值")
        
    else:
        print("❌ 部分配置集成测试失败:")
        for test_name, result in results.items():
            print(f"  {test_name}: {'✓' if result else '✗'}")
    
    print("\n配置集成测试完成！")
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
