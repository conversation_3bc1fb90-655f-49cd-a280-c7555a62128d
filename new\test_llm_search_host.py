#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 llm_search_host 的完整功能
验证API配置、MCP客户端连接和JSON格式返回
"""

import asyncio
import json
import sys
import os
import time

# 设置环境变量（确保API配置正确）
os.environ["SERP_API_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"
os.environ["OPENAI_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
os.environ["OPENAI_API_BASE"] = "https://api.shubiaobiao.cn/v1"
os.environ["GOOGLE_API_KEY"] = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"

# 添加路径以导入模块
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.search.llm_search_host import LLM_search, create_llm_search
    print("✅ 成功导入 llm_search_host 模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

async def test_mcp_client_connection():
    """测试MCP客户端连接"""
    print("\n=== 测试MCP客户端连接 ===")
    
    try:
        # 创建LLM_search实例
        llm_search = create_llm_search()
        
        # 测试获取MCP客户端
        client = await llm_search._get_mcp_client()
        print(f"✅ MCP客户端连接成功")
        
        # 测试获取可用工具
        tools = await llm_search._get_available_tools()
        print(f"✅ 获取到 {len(tools)} 个可用工具:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 清理连接
        await llm_search._cleanup_client()
        print("✅ MCP客户端连接测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP客户端连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_comprehensive_search():
    """测试完整搜索功能"""
    print("\n=== 测试完整搜索功能 ===")

    try:
        # 创建LLM_search实例
        llm_search = create_llm_search()
        print(f"✅ LLM_search实例创建成功")
        print(f"模型: {llm_search.model}")
        print(f"推理类型: {llm_search.infer_type}")
        print(f"内存功能: {llm_search.use_memory}")

        # 执行搜索
        topic = "transformer neural networks"
        description = "Research on transformer architecture in deep learning"

        print(f"\n开始搜索主题: {topic}")
        print(f"描述: {description}")

        start_time = time.time()
        results = await llm_search.comprehensive_search(
            topic=topic,
            description=description,
            top_n=5,  # 减少数量以加快测试
            similarity_threshold=70,
            min_length=200,
            max_length=50000
        )
        end_time = time.time()
        
        print(f"✅ 搜索完成，耗时: {end_time - start_time:.2f}秒")
        print(f"获得结果数量: {len(results)}")
        
        # 验证JSON格式
        if results:
            print("\n=== JSON格式验证 ===")
            
            # 检查第一个结果的格式
            first_result = results[0]
            print("第一个结果的字段:")
            for key, value in first_result.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
            
            # 验证必要字段
            required_fields = ['title', 'url', 'content', 'bibkey']
            missing_fields = [field for field in required_fields if field not in first_result]
            
            if missing_fields:
                print(f"❌ 缺少必要字段: {missing_fields}")
            else:
                print("✅ JSON格式验证通过")
            
            # 保存结果到文件
            output_file = "test_host_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "topic": topic,
                    "description": description,
                    "total_results": len(results),
                    "search_time": end_time - start_time,
                    "results": results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 结果已保存到 {output_file}")
            
        else:
            print("❌ 没有获得搜索结果")
            
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_functionality():
    """测试内存功能"""
    print("\n=== 测试内存功能 ===")
    
    try:
        llm_search = create_llm_search()
        
        # 测试内存统计
        stats = llm_search.get_memory_statistics()
        print(f"✅ 内存统计: {stats}")
        
        # 测试对话历史
        history = llm_search.get_conversation_history(limit=5)
        print(f"✅ 对话历史条数: {len(history)}")
        
        # 测试内存开关
        llm_search.set_memory_enabled(False)
        print("✅ 内存功能已禁用")
        
        llm_search.set_memory_enabled(True)
        print("✅ 内存功能已启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存功能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=== LLM Search Host 完整功能测试 ===")
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试MCP客户端连接
    test_results["mcp_connection"] = await test_mcp_client_connection()
    
    # 2. 测试内存功能
    test_results["memory_functionality"] = test_memory_functionality()
    
    # 3. 测试完整搜索功能
    test_results["comprehensive_search"] = await test_comprehensive_search()
    
    # 输出测试总结
    print("\n=== 测试总结 ===")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(test_results.values())
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
