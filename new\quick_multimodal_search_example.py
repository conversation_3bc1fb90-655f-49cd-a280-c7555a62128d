#!/usr/bin/env python3
"""
快速多模态推理文献搜索示例
简单演示如何使用LLM_search Host进行学术搜索
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_host import LLM_search

def quick_multimodal_search():
    """快速搜索多模态推理文献"""
    
    print("🔍 快速多模态推理文献搜索示例")
    print("=" * 50)
    
    # 1. 创建LLM搜索实例（使用配置文件中的默认模型）
    print("📱 初始化LLM搜索...")
    llm_search = LLM_search()
    print("✅ LLM搜索初始化完成")
    
    # 2. 定义搜索主题
    topic = "多模态推理综述"
    description = """
    搜索多模态推理(Multimodal Reasoning)相关的综述文献，包括：
    - 视觉语言模型(Vision-Language Models)
    - 多模态大语言模型(Multimodal LLMs)
    - 跨模态理解与推理
    - 最新技术进展和应用
    """
    
    print(f"\n🎯 搜索主题: {topic}")
    print(f"📝 搜索描述: {description}")
    
    try:
        # 3. 生成搜索查询
        print(f"\n🔍 生成搜索查询...")
        queries = llm_search.get_queries(topic=topic, description=description)
        
        print(f"✅ 生成了 {len(queries)} 个查询:")
        for i, query in enumerate(queries, 1):
            print(f"  {i}. {query}")
        
        # 4. 执行网络搜索
        print(f"\n🌐 执行网络搜索...")
        urls = llm_search.batch_web_search(
            queries=queries[:5],  # 使用前5个查询
            topic=topic,
            top_n=20  # 获取20个最相关的URL
        )
        
        print(f"✅ 找到 {len(urls)} 个相关链接")
        
        # 显示前几个URL
        print(f"\n🔗 前5个搜索结果:")
        for i, url in enumerate(urls[:5], 1):
            print(f"  {i}. {url}")
        
        # 5. 爬取和分析内容（可选，需要更多时间）
        analyze_content = input(f"\n❓ 是否要分析内容？这可能需要几分钟时间 (y/n): ").lower().strip()
        
        if analyze_content == 'y':
            print(f"\n📄 分析内容中...")
            results = llm_search.crawl_urls(
                topic=topic,
                url_list=urls[:10],  # 分析前10个URL
                top_n=5,  # 返回最相关的5篇文献
                similarity_threshold=70,
                min_length=300,
                max_length=15000
            )
            
            print(f"✅ 成功分析了 {len(results)} 篇相关文献")
            
            # 显示分析结果
            print(f"\n📚 分析结果:")
            for i, paper in enumerate(results, 1):
                title = paper.get('title', '未知标题')
                url = paper.get('url', '未知链接')
                score = paper.get('score', 0)
                content_preview = paper.get('content', '')[:150] + "..."
                
                print(f"\n📖 文献 {i}:")
                print(f"   标题: {title}")
                print(f"   链接: {url}")
                print(f"   相关度: {score:.1f}")
                print(f"   内容预览: {content_preview}")
            
            return results
        else:
            print("⏭️ 跳过内容分析")
            return urls
            
    except Exception as e:
        print(f"❌ 搜索过程中出现错误: {e}")
        print("💡 请检查:")
        print("   1. 网络连接是否正常")
        print("   2. API密钥是否正确设置")
        print("   3. MCP服务是否正常运行")
        return None

def demo_custom_search():
    """演示自定义搜索"""
    
    print(f"\n\n🎨 自定义搜索示例")
    print("=" * 50)
    
    # 让用户输入自定义主题
    custom_topic = input("🎯 请输入您想搜索的主题: ").strip()
    if not custom_topic:
        custom_topic = "计算机视觉与自然语言处理结合"
    
    custom_description = input("📝 请输入详细描述（可选，直接回车跳过）: ").strip()
    if not custom_description:
        custom_description = f"搜索关于{custom_topic}的最新研究进展和技术综述"
    
    print(f"\n🔍 开始搜索: {custom_topic}")
    
    try:
        llm_search = LLM_search()
        
        # 生成查询
        queries = llm_search.get_queries(topic=custom_topic, description=custom_description)
        print(f"✅ 生成了 {len(queries)} 个查询")
        
        # 执行搜索
        urls = llm_search.batch_web_search(
            queries=queries[:3],  # 使用前3个查询
            topic=custom_topic,
            top_n=10
        )
        
        print(f"✅ 找到 {len(urls)} 个相关链接")
        
        # 显示结果
        print(f"\n🔗 搜索结果:")
        for i, url in enumerate(urls, 1):
            print(f"  {i}. {url}")
            
        return urls
        
    except Exception as e:
        print(f"❌ 自定义搜索失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🚀 多模态推理文献搜索演示")
    print("使用LLM_search Host + MCP架构")
    print("=" * 60)
    
    # 检查环境
    print("🔧 环境检查:")
    openai_key = os.getenv("OPENAI_API_KEY")
    serp_key = os.getenv("SERP_API_KEY")
    
    if openai_key:
        print("✅ OPENAI_API_KEY 已设置")
    else:
        print("⚠️ OPENAI_API_KEY 未设置")
    
    if serp_key:
        print("✅ SERP_API_KEY 已设置")
    else:
        print("⚠️ SERP_API_KEY 未设置")
    
    if not openai_key or not serp_key:
        print("\n💡 请设置API密钥:")
        print("   export OPENAI_API_KEY='your_openai_key'")
        print("   export SERP_API_KEY='your_serp_key'")
        print("\n或者修改配置文件: config/llm_search_mcp_config.json")
        
        continue_anyway = input("\n❓ 是否继续演示？(y/n): ").lower().strip()
        if continue_anyway != 'y':
            return
    
    # 演示1: 预定义的多模态推理搜索
    print(f"\n" + "="*60)
    print("📚 演示1: 多模态推理综述搜索")
    print("="*60)
    
    results1 = quick_multimodal_search()
    
    # 演示2: 自定义搜索
    print(f"\n" + "="*60)
    print("🎨 演示2: 自定义主题搜索")
    print("="*60)
    
    results2 = demo_custom_search()
    
    # 总结
    print(f"\n" + "="*60)
    print("🎉 演示完成！")
    print("="*60)
    
    print("\n📋 使用总结:")
    print("1. ✅ LLM_search Host 可以自动生成专业的搜索查询")
    print("2. ✅ 支持批量网络搜索，获取相关链接")
    print("3. ✅ 可以分析网页内容，提取相关信息")
    print("4. ✅ 支持自定义搜索主题和描述")
    print("5. ✅ 使用MCP架构，配置灵活，扩展性强")
    
    print("\n🔧 技术特点:")
    print("• Host仅作为LLM调用器，不包含具体实现")
    print("• Server从配置文件读取所有设置")
    print("• Client作为纯通信桥梁")
    print("• 支持多种搜索引擎和模型配置")
    
    print("\n📚 适用场景:")
    print("• 学术文献搜索和综述")
    print("• 技术调研和市场分析")
    print("• 知识发现和信息整合")
    print("• 自动化研究助手")

if __name__ == "__main__":
    main()
