#!/usr/bin/env python3
"""
测试SerpAPI是否正常工作
"""
import os
import sys
import requests
import json

# 设置环境变量
os.environ["SERPAPI_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.LLM_search import LLM_search

def test_serpapi_direct():
    """直接测试SerpAPI"""
    print("🧪 直接测试SerpAPI...")
    
    serpapi_key = os.getenv("SERPAPI_KEY")
    print(f"🔑 SERPAPI_KEY: {serpapi_key[:10]}..." if serpapi_key else "❌ 没有找到SERPAPI_KEY")
    
    if not serpapi_key:
        print("❌ 没有SERPAPI_KEY，无法测试")
        return
    
    # 直接调用SerpAPI
    try:
        url = "https://serpapi.com/search"
        params = {
            "engine": "google",
            "q": "transformer attention mechanism",
            "api_key": serpapi_key,
            "num": 5
        }
        
        print(f"🌐 请求URL: {url}")
        print(f"📝 参数: {params}")
        
        response = requests.get(url, params=params)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取响应")
            print(f"📄 响应键: {list(data.keys())}")
            
            if "organic_results" in data:
                results = data["organic_results"]
                print(f"🔍 找到 {len(results)} 个搜索结果")
                for i, result in enumerate(results[:3]):
                    print(f"  {i+1}. {result.get('title', 'No title')}")
                    print(f"     {result.get('link', 'No link')}")
            else:
                print("❌ 响应中没有organic_results")
                print(f"📄 完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 直接测试SerpAPI失败: {e}")

def test_llm_search_class():
    """测试LLM_search类"""
    print("\n🧪 测试LLM_search类...")
    
    try:
        # 创建LLM_search实例
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            engine="google",
            each_query_result=5
        )
        
        print(f"🔑 serpapi_key: {llm_search.serpapi_key[:10]}..." if llm_search.serpapi_key else "❌ 没有serpapi_key")
        print(f"🔑 bing_key: {llm_search.bing_subscription_key[:10]}..." if llm_search.bing_subscription_key else "❌ 没有bing_key")
        
        # 测试单个查询
        print("🔍 测试单个web_search...")
        result = llm_search.web_search("transformer attention mechanism")
        print(f"📊 搜索结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"📄 结果键: {list(result.keys())}")
            print(f"🔢 结果数量: {len(result)}")
        else:
            print(f"📄 结果内容: {result}")
            
    except Exception as e:
        print(f"❌ LLM_search类测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始SerpAPI测试")
    test_serpapi_direct()
    test_llm_search_class()
    print("🏁 测试结束")
