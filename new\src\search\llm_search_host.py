import asyncio
import json
import logging
import os
import sys
import traceback
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import List, Literal, Optional, Dict, Any

from .llm_search_mcp_client import MCPClient, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_model_config():
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'model_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"Model config loaded from: {config_path}")
        return config
    except Exception as e:
        logger.warning(f"Failed to load model config, using defaults: {e}")
        return {
            "search": {
                "host_llm": {
                    "model": "gemini-2.0-flash-thinking-exp-01-21",
                    "infer_type": "OpenAI"
                }
            }
        }

MODEL_CONFIG = load_model_config()

class LLMSearchHost:

    def __init__(
        self,
        model: str = None,
        infer_type: str = None,
        max_workers: int = 10,
        use_memory: bool = True,
        max_context_messages: int = 10,
    ):
        # 从配置文件读取默认模型配置
        if model is None or infer_type is None:
            host_config = MODEL_CONFIG.get("search", {}).get("host_llm", {})
            model = model or host_config.get("model", "gemini-2.0-flash-thinking-exp-01-21")
            infer_type = infer_type or host_config.get("infer_type", "OpenAI")

        self.model = model
        self.infer_type = infer_type
        self.use_memory = use_memory
        self.request_wrapper = RequestWrapper(
            model=model,
            infer_type=infer_type,
            use_memory=use_memory,
            max_context_messages=max_context_messages
        )
        self._mcp_client: Optional[MCPClient] = None
        self._connection_lock = asyncio.Lock()

        logger.info(f"LLMSearchHost initialized with model: {model}, infer_type: {infer_type}, memory: {use_memory}")

    async def _get_mcp_client(self) -> MCPClient:
        """每次都创建新的MCP客户端连接，避免连接复用问题"""
        try:
            # 清理任何现有连接
            if self._mcp_client is not None:
                try:
                    if self._mcp_client.is_connected:
                        await self._mcp_client.disconnect()
                except Exception as e:
                    logger.debug(f"Error cleaning up old connection: {e}")
                finally:
                    self._mcp_client = None

            # 创建新连接
            self._mcp_client = await create_mcp_client_from_config()
            logger.debug("Created new MCP client connection")
            return self._mcp_client
        except Exception as e:
            logger.error(f"Failed to create MCP client: {e}")
            self._mcp_client = None
            raise

    async def _cleanup_client_unsafe(self):
        """不加锁的清理方法，仅在已持有锁的情况下调用"""
        if self._mcp_client:
            try:
                if self._mcp_client.is_connected:
                    await self._mcp_client.disconnect()
            except Exception as e:
                logger.warning(f"Error during client cleanup: {e}")
            finally:
                self._mcp_client = None

    async def _cleanup_client(self):
        """公共清理方法，带锁保护"""
        async with self._connection_lock:
            await self._cleanup_client_unsafe()
    
    async def _get_available_tools(self) -> List[dict]:
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    def _create_tool_selection_prompt(self, task_description: str, available_tools: List[dict]) -> str:
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""你是一个搜索智能体。用户给你一个搜索任务，你需要选择最合适的工具来完成。

可用工具：
{tools_info}

用户任务：{task_description}

请分析任务需求，选择最合适的工具，并提供调用参数。注意：不要在参数中包含配置信息（如engine、model等），这些由Server自动处理。

请按以下JSON格式返回：
{{
    "selected_tool": "工具名称",
    "arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "选择理由"
}}"""

    async def _llm_select_and_call_tool(self, task_description: str, **user_args) -> dict:
        try:
            available_tools = await self._get_available_tools()
            
            prompt = self._create_tool_selection_prompt(task_description, available_tools)
            response = await self.request_wrapper.async_request(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            try:
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_text = response[json_start:json_end].strip()
                else:
                    json_text = response.strip()
                
                decision = json.loads(json_text)
                
                arguments = decision["arguments"]
                arguments.update(user_args)
                
                client = await self._get_mcp_client()
                result = await client.call_tool(decision["selected_tool"], arguments)
                
                logger.info(f"LLM selected tool: {decision['selected_tool']}, reasoning: {decision.get('reasoning', 'N/A')}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response: {e}")
                client = await self._get_mcp_client()
                return await client.call_tool("generate_search_queries", user_args)
                
        except Exception as e:
            logger.error(f"Error in LLM tool selection: {e}")
            raise
        finally:
            await self._cleanup_client()

   

    def get_conversation_history(self, limit: Optional[int] = None):
        """获取当前模型的对话历史"""
        return self.request_wrapper.get_conversation_history(limit)

    def clear_conversation_history(self):
        """清除当前模型的对话历史"""
        self.request_wrapper.clear_conversation_history()
        logger.info(f"Cleared conversation history for MCP Host model: {self.model}")

    def get_memory_statistics(self):
        """获取内存使用统计"""
        return self.request_wrapper.get_memory_statistics()

    def export_conversation_history(self, output_file: str) -> bool:
        """导出对话历史到文件"""
        return self.request_wrapper.export_conversation_history(output_file)

    def set_memory_enabled(self, enabled: bool):
        """启用或禁用对话历史功能"""
        self.use_memory = enabled
        self.request_wrapper.use_memory = enabled
        logger.info(f"Memory functionality {'enabled' if enabled else 'disabled'} for MCP Host model: {self.model}")

    def _run_mcp_simple_sequential(self, task_description: str, topic: str, description: str,
                                  top_n: int, similarity_threshold: float, min_length: int, max_length: int) -> Dict[str, Any]:
        """简化的MCP顺序执行，每次调用后断开连接"""
        import subprocess
        import json
        import tempfile
        import os

        logger.info("🔧 使用简化的MCP顺序执行方式")

        # 创建简化的脚本内容
        script_content = f'''#!/usr/bin/env python3
import asyncio
import json
import sys
import os

# 添加项目路径并设置工作目录
sys.path.insert(0, r"{os.path.abspath(".")}")
os.chdir(r"{os.path.abspath(".")}")

from src.search.llm_search_mcp_client import MCPClient

async def simple_mcp_call(tool_name, tool_args):
    """简单的MCP调用，调用后立即断开"""
    try:
        # 加载MCP配置
        config_path = os.path.join(os.path.abspath("."), 'config', 'llm_search_mcp_config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            full_config = json.load(f)

        server_config = full_config["mcpServers"]["llm_search_mcp"]

        # 初始化并连接MCP客户端
        client = MCPClient(server_config)
        await client.connect()

        # 调用工具
        result = await client.call_tool(tool_name, tool_args)

        # 立即断开连接
        await client.disconnect()

        return result

    except Exception as e:
        print(f"❌ MCP调用失败: {{e}}")
        return None

async def sequential_mcp_search():
    """顺序MCP搜索主函数"""

    topic = "{topic}"
    description = "{description}"
    top_n = {top_n}

    print(f"🎯 开始顺序MCP搜索")
    print(f"📝 主题: {{topic}}")
    print(f"🎯 目标: {{top_n}}篇文献")

    try:
        # 第1步：生成搜索查询
        print("\\n🔧 步骤1: 生成搜索查询...")
        query_result = await simple_mcp_call("generate_search_queries", {{
            "topic": topic,
            "description": description
        }})

        if not query_result:
            print("❌ 查询生成失败")
            return {{}}

        queries = query_result.get('queries', [])[:3]  # 只取前3个查询，减少处理时间
        print(f"✅ 生成了 {{len(queries)}} 个查询")

        # 第2步：网络搜索
        print("\\n🔧 步骤2: 网络搜索...")
        search_result = await simple_mcp_call("web_search", {{
            "queries": queries,
            "topic": topic
        }})

        if not search_result:
            print("❌ 网络搜索失败")
            return {{}}

        urls = search_result.get('urls', [])
        print(f"✅ 搜索到 {{len(urls)}} 个URL")

        if not urls:
            print("⚠️ 没有搜索到URL")
            return {{
                'topic': topic,
                'description': description,
                'queries': queries,
                'search_results': search_result,
                'final_results': [],
                'query_count': len(queries),
                'result_count': 0
            }}

        # 第3步：分析搜索结果
        print("\\n🔧 步骤3: 分析搜索结果...")
        analyze_result = await simple_mcp_call("analyze_search_results", {{
            "urls": urls[:10],  # 只分析前10个URL
            "topic": topic,
            "max_results": min(top_n * 2, 10)  # 分析结果数量
        }})

        if not analyze_result:
            print("❌ 结果分析失败")
            return {{}}

        analyzed_urls = analyze_result.get('analyzed_urls', [])
        print(f"✅ 分析了 {{len(analyzed_urls)}} 个URL")

        if not analyzed_urls:
            print("⚠️ 没有分析出有效URL")
            return {{
                'topic': topic,
                'description': description,
                'queries': queries,
                'search_results': search_result,
                'analyzed_results': analyze_result,
                'final_results': [],
                'query_count': len(queries),
                'result_count': 0
            }}

        # 第4步：爬取内容
        print("\\n🔧 步骤4: 爬取内容...")
        crawl_result = await simple_mcp_call("crawl_urls", {{
            "topic": topic,
            "url_list": analyzed_urls[:top_n],  # 只爬取需要的数量
            "top_n": top_n
        }})

        if not crawl_result:
            print("❌ 内容爬取失败")
            return {{}}

        final_results = crawl_result.get('crawled_data', [])
        print(f"✅ 爬取了 {{len(final_results)}} 篇文献")

        # 返回最终结果
        search_results = {{
            'topic': topic,
            'description': description,
            'queries': queries,
            'search_results': search_result,
            'analyzed_results': analyze_result,
            'crawled_content': crawl_result,
            'final_results': final_results,
            'processing_metadata': {{
                'method': 'simple_sequential_mcp',
                'steps_completed': 4
            }}
        }}

        search_results['query_count'] = len(queries)
        search_results['result_count'] = len(final_results)

        return search_results

    except Exception as e:
        print(f"❌ 顺序MCP搜索失败: {{e}}")
        return {{}}

if __name__ == "__main__":
    result = asyncio.run(sequential_mcp_search())
    print(json.dumps(result, ensure_ascii=False, indent=2))
'''

        try:
            # 写入临时脚本
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(script_content)
                temp_script = f.name

            logger.debug(f"📝 创建临时脚本: {temp_script}")

            # 执行脚本
            result = subprocess.run(
                [sys.executable, temp_script],
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时，给相似度计算更多时间
                errors='replace',
                env=dict(os.environ, **{
                    'PYTHONPATH': os.pathsep.join(sys.path),
                    'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY', ''),
                    'OPENAI_API_BASE': os.getenv('OPENAI_API_BASE', ''),
                    'GOOGLE_API_KEY': os.getenv('GOOGLE_API_KEY', ''),
                    'SERP_API_KEY': os.getenv('SERP_API_KEY', ''),
                    'SERPAPI_KEY': os.getenv('SERPAPI_KEY', ''),
                })
            )

            if result.returncode == 0:
                try:
                    search_results = json.loads(result.stdout)
                    logger.info(f"✅ 简化MCP搜索完成，获得 {search_results.get('result_count', 0)} 个结果")
                    return search_results
                except json.JSONDecodeError as e:
                    logger.error(f"❌ 解析搜索结果JSON失败: {e}")
                    logger.error(f"原始输出: {result.stdout[:500]}")
                    return {}
            else:
                logger.error(f"❌ 简化MCP搜索失败，返回码: {result.returncode}")
                logger.error(f"错误输出: {result.stderr}")
                return {}

        except subprocess.TimeoutExpired:
            logger.error("❌ 简化MCP搜索超时")
            return {}
        except Exception as e:
            logger.error(f"❌ 简化MCP搜索异常: {e}")
            return {}
        finally:
            # 清理临时文件
            try:
                if 'temp_script' in locals():
                    os.unlink(temp_script)
                    logger.debug(f"🗑️ 清理临时脚本: {temp_script}")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ 清理临时文件失败: {cleanup_error}")

    def _run_mcp_multi_turn(self, task_description: str, topic: str, description: str,
                           top_n: int, similarity_threshold: float, min_length: int, max_length: int) -> Dict[str, Any]:
        """多轮MCP执行，让LLM自主选择工具并判断结束"""
        # 使用简化的顺序执行方式
        return self._run_mcp_simple_sequential(task_description, topic, description, top_n, similarity_threshold, min_length, max_length)

    async def search(self, topic: str, description: str = "", top_n: int = 20,
                     similarity_threshold: float = 80, min_length: int = 350,
                     max_length: int = 20000) -> Dict[str, Any]:
        """搜索流程 - Host内LLM通过MCP调用Server中的工具"""
        logger.info(f"🚀 开始搜索主题: '{topic}'")
        logger.info(f"📝 描述长度: {len(description)} 字符")
        logger.info(f"🎯 目标文献数: {top_n}")

        # 构建任务描述
        task_description = f"""
为研究主题进行完整的文献搜索和分析：

主题: {topic}
详细描述: {description}

请完成以下完整流程：
1. 分析主题和描述，生成最优的搜索查询策略
2. 执行网络搜索获取相关学术文献URL
3. 爬取和分析文献内容
4. 根据相关性筛选出最佳结果

目标: 获取{top_n}篇高质量相关文献
"""

        # 使用多轮MCP执行方式
        try:
            logger.info("🔧 使用多轮MCP执行方式，让LLM自主选择工具")
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(
                    executor,
                    self._run_mcp_multi_turn,
                    task_description, topic, description, top_n,
                    similarity_threshold, min_length, max_length
                )

            if result:
                logger.info(f"✅ 搜索完成: {result.get('query_count', 0)} 个查询生成")
                return result
            else:
                logger.warning("⚠️ 搜索未返回结果")
                return {}

        except Exception as e:
            logger.error(f"❌ 搜索失败: {e}")
            import traceback
            logger.error(f"📋 错误详情: {traceback.format_exc()}")
            return {}






def create_llm_search_host(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    max_workers: int = 10,
) -> LLMSearchHost:
    return LLMSearchHost(
        model=model,
        infer_type=infer_type,
        max_workers=max_workers
    )
