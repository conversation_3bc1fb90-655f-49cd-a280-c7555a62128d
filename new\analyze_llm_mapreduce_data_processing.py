#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 LLM_MapReduceV2 中爬虫数据处理流程与 survey_data JSON 文件的对比
"""

import json

def analyze_llm_mapreduce_processing():
    """分析 LLM_MapReduceV2 的数据处理流程"""
    print("=== LLM_MapReduceV2 爬虫数据处理流程分析 ===")
    print()
    
    print("1. 数据处理阶段:")
    stages = {
        "Stage 1": "URL 爬取 (_crawl_urls)",
        "Stage 2": "内容过滤和标题生成 (_process_filter_and_titles)",
        "Stage 3": "相似度评分 (_process_similarity_scores)",
        "Stage 4": "结果处理和保存 (_process_results)"
    }
    
    for stage, desc in stages.items():
        print(f"   {stage}: {desc}")
    
    print("\n2. 数据结构转换过程:")
    
    # 原始爬取数据结构
    raw_crawl_data = {
        "topic": "str - 研究主题",
        "url": "str - URL地址",
        "raw_content": "str - 原始网页内容",
        "error": "bool - 是否出错"
    }
    
    print("   a) 原始爬取数据 (_crawl_and_collect):")
    for key, desc in raw_crawl_data.items():
        print(f"      {key}: {desc}")
    
    # 过滤后数据结构
    filtered_data = {
        "topic": "str - 研究主题",
        "url": "str - URL地址", 
        "raw_content": "str - 原始内容",
        "title": "str - 提取的标题",
        "filtered": "str - 过滤后内容",
        "error": "bool - 是否出错"
    }
    
    print("\n   b) 过滤后数据 (_process_filter_and_title):")
    for key, desc in filtered_data.items():
        print(f"      {key}: {desc}")
    
    # 评分后数据结构
    scored_data = {
        "topic": "str - 研究主题",
        "url": "str - URL地址",
        "title": "str - 标题",
        "filtered": "str - 过滤后内容",
        "similarity": "int - 相似度评分",
        "error": "bool - 是否出错"
    }
    
    print("\n   c) 评分后数据 (_process_similarity_score):")
    for key, desc in scored_data.items():
        print(f"      {key}: {desc}")
    
    # 最终输出数据结构
    final_paper_data = {
        "title": "str - 从 data['title'] 获取",
        "url": "str - 从 data['url'] 获取",
        "txt": "str - 从 data['filtered'] 获取",
        "similarity": "int - 从 data['similarity'] 获取"
    }
    
    print("\n   d) 最终 paper_data 结构 (_process_results):")
    for key, desc in final_paper_data.items():
        print(f"      {key}: {desc}")
    
    # 输出文件结构
    output_structure = {
        "title": "str - 主题名称",
        "papers": "List[Dict] - 论文列表"
    }
    
    print("\n   e) 输出文件结构:")
    for key, desc in output_structure.items():
        print(f"      {key}: {desc}")
    
    return {
        "raw_crawl": raw_crawl_data,
        "filtered": filtered_data,
        "scored": scored_data,
        "final_paper": final_paper_data,
        "output": output_structure
    }

def analyze_survey_data_usage():
    """分析 Survey 类如何使用数据"""
    print("\n=== Survey 类数据使用分析 ===")
    print()
    
    print("1. Survey 类初始化过程:")
    print("   a) 从 json_data['papers'] 读取论文列表")
    print("   b) 为每个 paper 生成 bibkey (基于 title)")
    print("   c) 只保留有 'txt' 内容的论文")
    print("   d) 存储到 self.papers 字典中")
    
    print("\n2. Digest 类处理 paper_info:")
    digest_processing = {
        "title": "paper_info['title']",
        "bibkey": "paper_info['bibkey']",
        "abstract": "paper_info.get('abstract', '') 或 origin_content[:500]",
        "content": "预处理后的 paper_info['txt']",
        "origin_content": "预处理后的 paper_info['txt']",
        "origin_token": "paper_info.get('txt_token', MAX_TOKEN)"
    }
    
    print("   Digest 期望的 paper_info 字段:")
    for key, desc in digest_processing.items():
        print(f"      {key}: {desc}")
    
    print("\n3. 关键处理函数:")
    print("   a) pre_proc_paper(): 移除参考文献和引用")
    print("   b) _del_citation(): 删除引用标记")
    print("   c) proc_title_to_str(): 将标题转换为 bibkey")

def compare_data_flows():
    """对比两种数据流"""
    print("\n=== 数据流对比分析 ===")
    print()
    
    print("1. 字段映射对比:")
    print("   ┌─────────────────────────────┬─────────────────────────────┐")
    print("   │ LLM_MapReduceV2 输出        │ Survey 期望输入             │")
    print("   ├─────────────────────────────┼─────────────────────────────┤")
    print("   │ title                       │ title ✓                     │")
    print("   │ url                         │ url ✓                       │")
    print("   │ txt                         │ txt ✓                       │")
    print("   │ similarity                  │ (无对应字段)                │")
    print("   │ (无)                        │ bibkey (需生成)             │")
    print("   │ (无)                        │ abstract (需提取)           │")
    print("   │ (无)                        │ txt_token (需计算)          │")
    print("   └─────────────────────────────┴─────────────────────────────┘")
    
    print("\n2. 数据丰富度对比:")
    print("   a) LLM_MapReduceV2 输出:")
    print("      - 4个基本字段")
    print("      - 专注于内容和相关性")
    print("      - 包含相似度评分")
    
    print("\n   b) Survey 期望输入:")
    print("      - 需要更多元数据字段")
    print("      - 需要 bibkey 用于引用")
    print("      - 需要 abstract 用于摘要")
    print("      - 需要 token 计数用于长度控制")
    
    print("\n3. 兼容性分析:")
    print("   ✓ 基本兼容: title, url, txt 字段直接映射")
    print("   ⚠ 需要扩展: 缺少 bibkey, abstract, txt_token 等字段")
    print("   ⚠ 额外字段: similarity 字段在 Survey 中未使用")

def suggest_integration_solution():
    """建议整合解决方案"""
    print("\n=== 整合解决方案建议 ===")
    print()
    
    print("1. 扩展 LLM_MapReduceV2 输出格式:")
    enhanced_output = {
        "title": "保持原有",
        "url": "保持原有", 
        "txt": "保持原有",
        "similarity": "保持原有(新增字段)",
        "bibkey": "基于 title 生成",
        "abstract": "从 txt 前500字符提取",
        "txt_token": "计算 txt 的 token 数量",
        "txt_length": "计算 txt 的字符长度"
    }
    
    for key, desc in enhanced_output.items():
        print(f"   {key}: {desc}")
    
    print("\n2. 修改建议:")
    print("   a) 在 _process_results 函数中扩展 paper_data 结构")
    print("   b) 添加 bibkey 生成逻辑")
    print("   c) 添加 abstract 提取逻辑")
    print("   d) 添加 token 计算逻辑")
    
    print("\n3. 代码修改示例:")
    code_example = '''
    # 在 _process_results 中修改 paper_data 构建
    paper_data = {
        "title": data["title"],
        "url": data["url"],
        "txt": data["filtered"],
        "similarity": data.get("similarity", 0),
        # 新增字段
        "bibkey": proc_title_to_str(data["title"]),
        "abstract": data["filtered"][:500],  # 简单提取前500字符
        "txt_token": estimate_tokens(data["filtered"]),
        "txt_length": len(data["filtered"])
    }
    '''
    print(code_example)

def main():
    """主函数"""
    llm_mapreduce_data = analyze_llm_mapreduce_processing()
    analyze_survey_data_usage()
    compare_data_flows()
    suggest_integration_solution()

if __name__ == '__main__':
    main()
