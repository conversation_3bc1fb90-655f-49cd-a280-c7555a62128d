#!/usr/bin/env python3
"""
详细的MCP工具调用测试
确保llm_search_host能正确调用MCP工具
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.llm_search_mcp_client import create_mcp_client_from_config

# 配置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_mcp_tools_detailed():
    """详细测试MCP工具调用"""
    logger.info("🚀 开始详细测试MCP工具调用")
    logger.info("=" * 80)
    
    client = None
    try:
        # 创建MCP客户端
        logger.info("🔧 创建MCP客户端...")
        client = await create_mcp_client_from_config()
        logger.info("✅ MCP客户端创建成功")
        
        # 获取可用工具
        logger.info("📋 获取可用工具...")
        tools_response = await client.list_tools()
        logger.info(f"工具响应类型: {type(tools_response)}")
        
        if hasattr(tools_response, 'tools'):
            tools = tools_response.tools
            logger.info(f"工具数量: {len(tools)}")
            for i, tool in enumerate(tools):
                logger.info(f"  工具 {i+1}: {tool.name} - {tool.description}")
        else:
            tools = tools_response
            logger.info(f"工具数量: {len(tools)}")
            for i, tool in enumerate(tools):
                logger.info(f"  工具 {i+1}: {tool.get('name', 'unknown')} - {tool.get('description', 'no description')}")
        
        # 测试generate_search_queries工具
        logger.info("=" * 80)
        logger.info("🔍 测试generate_search_queries工具...")
        
        test_topic = "机器学习基础"
        test_description = "机器学习的基本概念、算法和应用"
        
        logger.info(f"测试主题: {test_topic}")
        logger.info(f"测试描述: {test_description}")
        
        result = await client.call_tool(
            "generate_search_queries",
            {
                "topic": test_topic,
                "description": test_description,
                "num_queries": 5
            }
        )
        
        logger.info(f"工具调用结果类型: {type(result)}")
        if result:
            if hasattr(result, 'content'):
                logger.info(f"结果内容数量: {len(result.content)}")
                for i, content in enumerate(result.content):
                    logger.info(f"  内容 {i+1}: {type(content)}")
                    if hasattr(content, 'text'):
                        logger.info(f"    文本长度: {len(content.text)}")
                        logger.info(f"    文本预览: {content.text[:200]}...")
                        
                        # 尝试解析JSON
                        try:
                            import json
                            parsed = json.loads(content.text)
                            logger.info(f"    JSON解析成功: {type(parsed)}")
                            if isinstance(parsed, dict):
                                logger.info(f"    JSON字段: {list(parsed.keys())}")
                                if 'queries' in parsed:
                                    logger.info(f"    查询数量: {len(parsed['queries'])}")
                                    for j, query in enumerate(parsed['queries'][:3]):
                                        logger.info(f"      查询 {j+1}: {query}")
                        except json.JSONDecodeError as e:
                            logger.warning(f"    JSON解析失败: {e}")
            else:
                logger.info(f"结果: {result}")
        else:
            logger.warning("工具调用返回空结果")
        
        logger.info("=" * 80)
        logger.info("✅ MCP工具调用测试完成")
        return True
        
    except Exception as e:
        logger.error("=" * 80)
        logger.error(f"❌ MCP工具调用测试失败: {e}")
        import traceback
        logger.error(f"📋 错误详情:\n{traceback.format_exc()}")
        logger.error("=" * 80)
        return False
    finally:
        if client:
            try:
                await client.disconnect()
                logger.info("🔌 MCP客户端已断开连接")
            except Exception as e:
                logger.warning(f"⚠️ 断开连接时出错: {e}")

async def main():
    """主函数"""
    logger.info("🎯 开始详细MCP工具调用测试")
    logger.info("目标: 确保MCP工具能正确调用并返回结果")
    logger.info("=" * 80)
    
    success = await test_mcp_tools_detailed()
    
    if success:
        logger.info("🎉 所有测试通过!")
        return True
    else:
        logger.error("💥 测试失败!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
