"""
对话历史存储管理器
负责管理不同LLM模型的对话历史，支持持久化存储到JSON文件
"""

import json
import os
import threading
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


def load_memory_config(config_file: str = "memory_config.json") -> Dict[str, Any]:
    """加载内存配置文件"""
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"Failed to load memory config from {config_file}: {e}")

    # 返回默认配置
    return {
        "memory_settings": {
            "enabled": True,
            "memory_file": "memory.json",
            "max_history_per_model": 1000,
            "max_context_messages": 10,
            "auto_backup": True
        },
        "model_specific_settings": {},
        "storage_optimization": {
            "compress_old_conversations": False,
            "max_file_size_mb": 100,
            "auto_cleanup_enabled": False
        }
    }


class MemoryManager:
    """
    对话历史管理器
    
    功能：
    1. 按模型分别存储对话历史
    2. 支持持久化到JSON文件
    3. 线程安全的读写操作
    4. 自动备份和恢复
    """
    
    def __init__(self, memory_file: str = "memory.json", max_history_per_model: int = 1000,
                 config_file: str = "memory_config.json"):
        # 加载配置
        self.config = load_memory_config(config_file)
        memory_settings = self.config.get("memory_settings", {})

        self.memory_file = memory_settings.get("memory_file", memory_file)
        self.max_history_per_model = memory_settings.get("max_history_per_model", max_history_per_model)
        self.auto_backup = memory_settings.get("auto_backup", True)

        self._lock = threading.RLock()
        self._memory_cache = {}

        # 确保目录存在
        memory_dir = os.path.dirname(os.path.abspath(self.memory_file))
        if memory_dir:
            os.makedirs(memory_dir, exist_ok=True)

        # 加载现有的对话历史
        self._load_memory()

        logger.info(f"MemoryManager initialized with file: {self.memory_file}, config: {config_file}")
    
    def _load_memory(self):
        """从文件加载对话历史"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    self._memory_cache = json.load(f)
                logger.info(f"Loaded memory from {self.memory_file}")
            else:
                self._memory_cache = {}
                logger.info("No existing memory file found, starting with empty memory")
        except Exception as e:
            logger.error(f"Failed to load memory from {self.memory_file}: {e}")
            self._memory_cache = {}
    
    def _save_memory(self):
        """保存对话历史到文件"""
        try:
            # 创建备份（如果启用）
            if self.auto_backup and os.path.exists(self.memory_file):
                backup_file = f"{self.memory_file}.backup"
                import shutil
                shutil.copy2(self.memory_file, backup_file)

            # 检查文件大小限制
            storage_config = self.config.get("storage_optimization", {})
            max_size_mb = storage_config.get("max_file_size_mb", 100)

            # 序列化前清理不可序列化的对象
            serializable_cache = self._make_serializable(self._memory_cache)

            # 保存新的内容
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_cache, f, ensure_ascii=False, indent=2)

            # 检查文件大小
            file_size_mb = os.path.getsize(self.memory_file) / (1024 * 1024)
            if file_size_mb > max_size_mb:
                logger.warning(f"Memory file size ({file_size_mb:.2f}MB) exceeds limit ({max_size_mb}MB)")
                self._cleanup_old_conversations()

            logger.debug(f"Memory saved to {self.memory_file}")

        except Exception as e:
            logger.error(f"Failed to save memory to {self.memory_file}: {e}")
            # 尝试恢复备份
            backup_file = f"{self.memory_file}.backup"
            if os.path.exists(backup_file):
                import shutil
                shutil.copy2(backup_file, self.memory_file)
                logger.info("Restored from backup file")

    def _make_serializable(self, obj):
        """将对象转换为可JSON序列化的格式"""
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                result[key] = self._make_serializable(value)
            return result
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # 对于有__dict__属性的对象（如CompletionUsage），转换为字典
            try:
                return self._make_serializable(obj.__dict__)
            except:
                return str(obj)
        elif hasattr(obj, '_asdict'):
            # 对于namedtuple等
            try:
                return self._make_serializable(obj._asdict())
            except:
                return str(obj)
        else:
            # 对于基本类型，直接返回
            try:
                json.dumps(obj)  # 测试是否可序列化
                return obj
            except (TypeError, ValueError):
                return str(obj)

    def add_conversation(self, model: str, messages: List[Dict[str, str]],
                        response: str, metadata: Optional[Dict[str, Any]] = None):
        """
        添加一次对话记录

        Args:
            model: 模型名称
            messages: 输入消息列表
            response: LLM响应
            metadata: 额外的元数据（如token使用量、调用时间等）
        """
        with self._lock:
            if model not in self._memory_cache:
                self._memory_cache[model] = []

            conversation_record = {
                "timestamp": datetime.now().isoformat(),
                "messages": messages,
                "response": response,
                "metadata": metadata or {}
            }

            self._memory_cache[model].append(conversation_record)

            # 获取模型特定的历史限制
            model_config = self.config.get("model_specific_settings", {}).get(model, {})
            max_history = model_config.get("max_history", self.max_history_per_model)

            # 限制历史记录数量
            if len(self._memory_cache[model]) > max_history:
                self._memory_cache[model] = self._memory_cache[model][-max_history:]

            # 保存到文件
            self._save_memory()

            logger.debug(f"Added conversation for model {model}, total conversations: {len(self._memory_cache[model])}")
    
    def get_conversation_history(self, model: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取指定模型的对话历史
        
        Args:
            model: 模型名称
            limit: 返回的最大记录数，None表示返回所有
            
        Returns:
            对话历史列表
        """
        with self._lock:
            if model not in self._memory_cache:
                return []
            
            history = self._memory_cache[model]
            if limit is not None:
                history = history[-limit:]
            
            return history.copy()
    
    def get_recent_context(self, model: str, max_messages: Optional[int] = None) -> List[Dict[str, str]]:
        """
        获取最近的对话上下文，用于继续对话

        Args:
            model: 模型名称
            max_messages: 最大消息数量，None表示使用配置中的默认值

        Returns:
            格式化的消息列表，可直接用于LLM调用
        """
        with self._lock:
            if model not in self._memory_cache:
                return []

            # 使用配置中的最大消息数
            if max_messages is None:
                max_messages = self.get_max_context_messages(model)

            recent_conversations = self._memory_cache[model][-max_messages//2:]
            context_messages = []

            for conv in recent_conversations:
                # 添加用户消息
                if conv["messages"]:
                    context_messages.extend(conv["messages"])

                # 添加助手响应
                context_messages.append({
                    "role": "assistant",
                    "content": conv["response"]
                })

            # 限制总消息数量
            if len(context_messages) > max_messages:
                context_messages = context_messages[-max_messages:]

            return context_messages
    
    def clear_model_history(self, model: str):
        """清除指定模型的对话历史"""
        with self._lock:
            if model in self._memory_cache:
                del self._memory_cache[model]
                self._save_memory()
                logger.info(f"Cleared history for model: {model}")
    
    def get_all_models(self) -> List[str]:
        """获取所有有对话历史的模型列表"""
        with self._lock:
            return list(self._memory_cache.keys())
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取对话历史统计信息"""
        with self._lock:
            stats = {}
            for model, conversations in self._memory_cache.items():
                stats[model] = {
                    "total_conversations": len(conversations),
                    "first_conversation": conversations[0]["timestamp"] if conversations else None,
                    "last_conversation": conversations[-1]["timestamp"] if conversations else None
                }
            return stats
    
    def export_history(self, model: str, output_file: str):
        """导出指定模型的对话历史到文件"""
        history = self.get_conversation_history(model)
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            logger.info(f"Exported {len(history)} conversations for model {model} to {output_file}")
        except Exception as e:
            logger.error(f"Failed to export history: {e}")

    def _cleanup_old_conversations(self):
        """清理旧的对话记录以控制文件大小"""
        storage_config = self.config.get("storage_optimization", {})
        if not storage_config.get("auto_cleanup_enabled", False):
            return

        cleanup_age_days = storage_config.get("cleanup_age_days", 30)
        cutoff_date = datetime.now() - timedelta(days=cleanup_age_days)

        with self._lock:
            total_removed = 0
            for model in list(self._memory_cache.keys()):
                conversations = self._memory_cache[model]
                original_count = len(conversations)

                # 保留最近的对话
                self._memory_cache[model] = [
                    conv for conv in conversations
                    if datetime.fromisoformat(conv["timestamp"]) > cutoff_date
                ]

                removed = original_count - len(self._memory_cache[model])
                total_removed += removed

                if removed > 0:
                    logger.info(f"Cleaned up {removed} old conversations for model {model}")

            if total_removed > 0:
                logger.info(f"Total cleaned up: {total_removed} conversations")
                self._save_memory()

    def get_model_config(self, model: str) -> Dict[str, Any]:
        """获取指定模型的配置"""
        return self.config.get("model_specific_settings", {}).get(model, {})

    def get_max_context_messages(self, model: str) -> int:
        """获取指定模型的最大上下文消息数"""
        model_config = self.get_model_config(model)
        default_max = self.config.get("memory_settings", {}).get("max_context_messages", 10)
        return model_config.get("max_context_messages", default_max)

    def is_memory_enabled(self) -> bool:
        """检查内存功能是否启用"""
        return self.config.get("memory_settings", {}).get("enabled", True)


# 全局内存管理器实例
_global_memory_manager = None


def get_memory_manager(memory_file: str = "memory.json") -> MemoryManager:
    """获取全局内存管理器实例"""
    global _global_memory_manager
    if _global_memory_manager is None:
        _global_memory_manager = MemoryManager(memory_file)
    return _global_memory_manager


def add_conversation_to_memory(model: str, messages: List[Dict[str, str]], 
                              response: str, metadata: Optional[Dict[str, Any]] = None):
    """便捷函数：添加对话到全局内存管理器"""
    memory_manager = get_memory_manager()
    memory_manager.add_conversation(model, messages, response, metadata)


def get_conversation_context(model: str, max_messages: Optional[int] = None) -> List[Dict[str, str]]:
    """便捷函数：获取对话上下文"""
    memory_manager = get_memory_manager()
    return memory_manager.get_recent_context(model, max_messages)
