#!/usr/bin/env python3
"""
快速开始指南 - 5分钟上手LLM Search Host

选择你的使用场景：
1. 生产环境/固定搜索模式 → 使用简化版本
2. 研究环境/需要AI决策 → 使用复杂版本
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def quick_start_simple():
    """快速开始 - 简化版本（推荐用于生产环境）"""
    print("🚀 快速开始 - 简化版本")
    print("适用于：生产环境、固定搜索模式、追求性能")
    print("-" * 50)
    
    try:
        # 1. 导入并创建实例
        from src.search.llm_search_simple_host import SimpleLLMSearch
        
        llm_search = SimpleLLMSearch(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        print("✓ 创建SimpleLLMSearch实例成功")
        
        # 2. 生成搜索查询
        queries = llm_search.get_queries(
            topic="Python编程",
            description="Python编程语言的学习资源和最佳实践"
        )
        print(f"✓ 生成了 {len(queries)} 个搜索查询")
        print(f"  示例查询: {queries[0] if queries else 'N/A'}")
        
        # 3. 执行搜索
        urls = llm_search.batch_web_search(
            queries=queries[:2],  # 只用前2个查询
            topic="Python编程",
            top_n=5
        )
        print(f"✓ 搜索完成，找到 {len(urls)} 个相关URL")
        print(f"  示例URL: {urls[0][:60]}..." if urls else "  无结果")
        
        print("\n🎉 简化版本快速开始成功！")
        return True
        
    except Exception as e:
        print(f"❌ 简化版本失败: {e}")
        print("💡 请检查API密钥设置和网络连接")
        return False

def quick_start_complex():
    """快速开始 - 复杂版本（推荐用于研究环境）"""
    print("\n🧠 快速开始 - 复杂版本")
    print("适用于：研究环境、需要AI决策、复杂任务")
    print("-" * 50)
    
    try:
        # 1. 导入并创建实例
        from src.search.llm_search_host import LLM_search
        
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        print("✓ 创建LLM_search实例成功")
        
        # 2. AI智能生成搜索查询
        queries = llm_search.get_queries(
            topic="区块链技术",
            description="区块链技术的原理、应用和发展趋势"
        )
        print(f"✓ AI生成了 {len(queries)} 个智能查询")
        print(f"  示例查询: {queries[0] if queries else 'N/A'}")
        
        # 3. AI智能执行搜索
        urls = llm_search.batch_web_search(
            queries=queries[:2],  # 只用前2个查询
            topic="区块链技术",
            top_n=5
        )
        print(f"✓ AI搜索完成，找到 {len(urls)} 个相关URL")
        print(f"  示例URL: {urls[0][:60]}..." if urls else "  无结果")
        
        print("\n🎉 复杂版本快速开始成功！")
        return True
        
    except Exception as e:
        print(f"❌ 复杂版本失败: {e}")
        print("💡 请检查API密钥设置和网络连接")
        return False

def setup_guide():
    """设置指南"""
    print("\n⚙️ 设置指南")
    print("=" * 50)
    print("""
1. 设置API密钥（必需）：

   # LLM API密钥（至少设置一个）
   export OPENAI_API_KEY="your_openai_key"
   export GOOGLE_API_KEY="your_google_key"
   
   # 搜索引擎API密钥（至少设置一个）
   export SERP_API_KEY="your_serp_key"
   export BING_SEARCH_V7_SUBSCRIPTION_KEY="your_bing_key"

2. 安装依赖：
   pip install -r requirements.txt

3. 选择版本：
   • 生产环境 → SimpleLLMSearch
   • 研究环境 → LLM_search
    """)

def usage_examples():
    """使用示例"""
    print("\n📖 使用示例")
    print("=" * 50)
    print("""
# 简化版本 - 直接高效
from src.search.llm_search_simple_host import SimpleLLMSearch

llm_search = SimpleLLMSearch()
queries = llm_search.get_queries("机器学习")
urls = llm_search.batch_web_search(queries, "机器学习", 10)

# 复杂版本 - AI智能
from src.search.llm_search_host import LLM_search

llm_search = LLM_search()
queries = llm_search.get_queries("深度学习", "研究神经网络架构")
urls = llm_search.batch_web_search(queries, "深度学习", 15)
    """)

def troubleshooting():
    """故障排除"""
    print("\n🔧 故障排除")
    print("=" * 50)
    print("""
常见问题及解决方案：

1. API密钥错误
   ❌ No valid search engine key provided
   ✅ 设置 SERP_API_KEY 或 BING_SEARCH_V7_SUBSCRIPTION_KEY

2. 连接超时
   ❌ Failed to connect to MCP server
   ✅ 检查网络连接，重试

3. 模型不可用
   ❌ Model not available
   ✅ 检查 OPENAI_API_KEY 或 GOOGLE_API_KEY

4. 导入错误
   ❌ ModuleNotFoundError
   ✅ 检查项目路径和依赖安装

5. 性能问题
   ❌ 响应太慢
   ✅ 使用简化版本，减少查询数量
    """)

def main():
    """主函数"""
    print("🎯 LLM Search Host 快速开始指南")
    print("=" * 60)
    
    # 设置指南
    setup_guide()
    
    # 快速开始测试
    print("\n🧪 快速测试")
    print("=" * 50)
    
    simple_success = quick_start_simple()
    complex_success = quick_start_complex()
    
    # 使用示例
    usage_examples()
    
    # 故障排除
    troubleshooting()
    
    # 总结
    print("\n📋 总结")
    print("=" * 50)
    
    if simple_success and complex_success:
        print("🎉 恭喜！两种版本都运行成功")
        print("你现在可以根据需求选择合适的版本：")
        print("• 生产环境 → SimpleLLMSearch")
        print("• 研究环境 → LLM_search")
    elif simple_success:
        print("✅ 简化版本运行成功")
        print("❌ 复杂版本运行失败，建议先使用简化版本")
    elif complex_success:
        print("❌ 简化版本运行失败")
        print("✅ 复杂版本运行成功")
    else:
        print("❌ 两种版本都运行失败")
        print("请按照故障排除指南检查配置")
    
    print("\n📚 更多资源：")
    print("• 详细使用指南: docs/host_usage_guide.md")
    print("• 完整示例: example_host_usage.py")
    print("• 架构对比: test_architecture_comparison.py")
    print("• 架构文档: docs/corrected_mcp_architecture.md")

if __name__ == "__main__":
    main()
