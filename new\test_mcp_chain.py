#!/usr/bin/env python3
"""
测试完整的MCP链路：llm_search_host -> llm_search_client -> llm_search_server
"""

import asyncio
import json
import logging
import os
import sys

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.llm_search_host import LLMSearchHost, create_llm_search_host

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_client_connection():
    """测试MCP客户端连接"""
    print("🔧 测试MCP客户端连接...")
    
    try:
        from src.search.llm_search_mcp_client import create_mcp_client_from_config
        
        client = await create_mcp_client_from_config()
        print("✅ MCP客户端连接成功")
        
        # 测试列出工具
        tools = await client.list_tools()
        print(f"📋 可用工具: {[tool['name'] for tool in tools]}")
        
        # 测试调用一个简单工具
        if tools:
            tool_name = tools[0]['name']
            print(f"🔧 测试调用工具: {tool_name}")
            
            # 根据工具名称准备参数
            if tool_name == "generate_search_queries":
                result = await client.call_tool(tool_name, {
                    "topic": "machine learning",
                    "description": "Basic ML concepts"
                })
            elif tool_name == "web_search":
                result = await client.call_tool(tool_name, {
                    "queries": ["machine learning basics"],
                    "topic": "machine learning",
                    "top_n": 5
                })
            else:
                print(f"⚠️ 未知工具类型: {tool_name}")
                result = None
            
            if result:
                print(f"✅ 工具调用成功，结果类型: {type(result)}")
                if isinstance(result, dict) and 'queries' in result:
                    print(f"📝 生成的查询: {result['queries'][:3]}...")  # 只显示前3个
                elif isinstance(result, dict) and 'urls' in result:
                    print(f"🔗 找到的URL数量: {len(result['urls'])}")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ MCP客户端测试失败: {e}")
        logger.error(f"MCP client test failed: {e}", exc_info=True)
        return False

async def test_llm_search_host():
    """测试LLMSearchHost"""
    print("\n🏠 测试LLMSearchHost...")
    
    try:
        # 创建host实例
        host = create_llm_search_host()
        print("✅ LLMSearchHost创建成功")
        
        # 测试搜索功能
        print("🔍 测试搜索功能...")
        results = await host.search(
            topic="machine learning",
            description="Basic concepts in machine learning",
            top_n=3
        )

        print(f"✅ 搜索完成，结果数量: {len(results)}")
        if results:
            print(f"📄 第一个结果标题: {results[0].get('title', 'N/A')}")
        
        # 清理
        await host._cleanup_client()
        return True
        
    except Exception as e:
        print(f"❌ LLMSearchHost测试失败: {e}")
        logger.error(f"LLMSearchHost test failed: {e}", exc_info=True)
        return False

async def test_search_functionality():
    """测试搜索功能"""
    print("\n🔍 测试搜索功能...")

    try:
        host = create_llm_search_host()

        # 测试搜索功能
        results = await host.search(
            topic="neural networks",
            description="Deep learning and neural network architectures",
            top_n=5
        )

        print(f"✅ 搜索完成，结果数量: {len(results)}")

        # 验证结果格式
        if results:
            first_result = results[0]
            required_fields = ['title', 'url', 'content', 'bibkey', 'abstract', 'similarity_score', 'token_count']
            missing_fields = [field for field in required_fields if field not in first_result]

            if not missing_fields:
                print("✅ 结果格式验证通过")
            else:
                print(f"⚠️ 缺少字段: {missing_fields}")

            print(f"📊 结果示例:")
            print(f"  标题: {first_result.get('title', 'N/A')}")
            print(f"  相似度: {first_result.get('similarity_score', 'N/A')}")
            print(f"  Token数: {first_result.get('token_count', 'N/A')}")

        await host._cleanup_client()
        return True

    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        logger.error(f"Search test failed: {e}", exc_info=True)
        return False

async def test_api_configuration():
    """测试API配置"""
    print("\n⚙️ 测试API配置...")
    
    try:
        # 检查配置文件
        config_path = "config/llm_search_mcp_config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必要的API密钥
            env_vars = config.get("servers", {}).get("llm_search_server", {}).get("env", {})
            required_keys = ["SERP_API_KEY", "OPENAI_API_KEY", "OPENAI_API_BASE"]
            
            missing_keys = [key for key in required_keys if not env_vars.get(key)]
            
            if not missing_keys:
                print("✅ API配置完整")
                return True
            else:
                print(f"⚠️ 缺少API密钥: {missing_keys}")
                return False
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            return False
            
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始MCP链路测试\n")
    
    # 测试结果
    results = {}
    
    # 1. 测试API配置
    results['api_config'] = await test_api_configuration()
    
    # 2. 测试MCP客户端连接
    results['mcp_client'] = await test_mcp_client_connection()
    
    # 3. 测试LLMSearchHost
    results['llm_search_host'] = await test_llm_search_host()
    
    # 4. 测试搜索功能
    results['search_functionality'] = await test_search_functionality()
    
    # 总结结果
    print("\n📊 测试结果总结:")
    print("=" * 50)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    all_passed = all(results.values())
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有测试通过！MCP链路工作正常")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
