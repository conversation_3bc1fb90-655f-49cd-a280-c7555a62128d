#!/usr/bin/env python3
"""
Host V3 完整流程测试
测试LLM自主搜索的完整工作流程，包括：
1. 基础功能测试
2. 多轮自主搜索测试  
3. 错误处理测试
4. 性能和稳定性测试
"""

import asyncio
import logging
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_host import LLMSearchHostV3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'host_v3_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class HostV3CompleteTester:
    """Host V3 完整测试器"""
    
    def __init__(self):
        self.host = None
        self.test_results = {
            "basic_tests": {},
            "autonomous_search_tests": {},
            "error_handling_tests": {},
            "performance_tests": {}
        }
        
    async def setup(self):
        """初始化测试环境"""
        try:
            self.host = LLMSearchHostV3()
            logger.info("✅ Host V3 初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ Host V3 初始化失败: {e}")
            return False
    
    async def test_basic_functionality(self):
        """测试基础功能"""
        logger.info("🧪 开始基础功能测试")
        
        tests = [
            ("host_initialization", "Host初始化测试"),
            ("mcp_client_creation", "MCP客户端创建测试"),
            ("tool_availability", "工具可用性测试"),
            ("llm_decision_making", "LLM决策制定测试")
        ]
        
        for test_name, test_desc in tests:
            try:
                logger.info(f"📋 执行: {test_desc}")
                
                if test_name == "host_initialization":
                    # 测试Host初始化
                    assert self.host is not None
                    assert hasattr(self.host, 'search')
                    
                elif test_name == "mcp_client_creation":
                    # 测试MCP客户端创建
                    client = await self.host._create_mcp_client()
                    assert client is not None
                    await client.disconnect()
                    
                elif test_name == "tool_availability":
                    # 测试工具可用性
                    client = await self.host._create_mcp_client()
                    tools = await client.list_tools()
                    expected_tools = ["generate_search_queries", "web_search", "crawl_urls", "analyze_search_results"]
                    for tool in expected_tools:
                        assert any(t["name"] == tool for t in tools), f"工具 {tool} 不可用"
                    await client.disconnect()

                elif test_name == "llm_decision_making":
                    # 测试LLM决策制定 - 简化测试，只验证方法存在
                    assert hasattr(self.host, 'search'), "search方法不存在"
                    assert hasattr(self.host, '_llm_plan_search'), "_llm_plan_search方法不存在"
                
                self.test_results["basic_tests"][test_name] = {"status": "PASS", "message": "测试通过"}
                logger.info(f"✅ {test_desc} - 通过")
                
            except Exception as e:
                self.test_results["basic_tests"][test_name] = {"status": "FAIL", "message": str(e)}
                logger.error(f"❌ {test_desc} - 失败: {e}")
    
    async def test_autonomous_search(self):
        """测试自主搜索功能"""
        logger.info("🤖 开始自主搜索测试")
        
        test_cases = [
            {
                "name": "simple_search",
                "topic": "machine learning basics",
                "description": "简单机器学习搜索测试",
                "expected_rounds": 3
            },
            {
                "name": "complex_search", 
                "topic": "transformer architecture optimization",
                "description": "复杂Transformer优化搜索测试",
                "expected_rounds": 5
            }
        ]
        
        for case in test_cases:
            try:
                logger.info(f"📋 执行搜索测试: {case['description']}")
                start_time = time.time()
                
                result = await self.host.search(
                    topic=case["topic"],
                    description=case["description"]
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 验证结果
                assert result is not None
                assert "final_results" in result
                assert "processing_metadata" in result
                assert result["processing_metadata"]["turns"] > 0
                
                self.test_results["autonomous_search_tests"][case["name"]] = {
                    "status": "PASS",
                    "execution_time": execution_time,
                    "turns": result["processing_metadata"]["turns"],
                    "results_count": len(result.get("final_results", []))
                }
                
                logger.info(f"✅ {case['description']} - 通过")
                logger.info(f"   执行时间: {execution_time:.2f}秒")
                logger.info(f"   搜索轮数: {result['processing_metadata']['turns']}")
                logger.info(f"   结果数量: {len(result.get('final_results', []))}")
                
            except Exception as e:
                self.test_results["autonomous_search_tests"][case["name"]] = {
                    "status": "FAIL", 
                    "message": str(e)
                }
                logger.error(f"❌ {case['description']} - 失败: {e}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        logger.info("🛡️ 开始错误处理测试")
        
        error_tests = [
            {
                "name": "invalid_topic",
                "topic": "",
                "description": "空主题测试"
            },
            {
                "name": "network_resilience", 
                "topic": "test network resilience",
                "description": "网络韧性测试"
            }
        ]
        
        for test in error_tests:
            try:
                logger.info(f"📋 执行错误处理测试: {test['description']}")
                
                if test["name"] == "invalid_topic":
                    # 测试空主题处理
                    result = await self.host.search(topic="", description="测试空主题")
                    # 应该有适当的错误处理
                    
                elif test["name"] == "network_resilience":
                    # 测试网络韧性（正常执行，观察错误恢复）
                    result = await self.host.search(
                        topic=test["topic"],
                        description=test["description"]
                    )
                
                self.test_results["error_handling_tests"][test["name"]] = {
                    "status": "PASS",
                    "message": "错误处理正常"
                }
                logger.info(f"✅ {test['description']} - 通过")
                
            except Exception as e:
                # 某些错误是预期的
                self.test_results["error_handling_tests"][test["name"]] = {
                    "status": "EXPECTED_ERROR",
                    "message": str(e)
                }
                logger.info(f"⚠️ {test['description']} - 预期错误: {e}")
    
    async def test_performance(self):
        """测试性能"""
        logger.info("⚡ 开始性能测试")
        
        try:
            # 并发搜索测试
            logger.info("📋 执行并发搜索测试")
            
            topics = [
                "artificial intelligence",
                "machine learning", 
                "deep learning"
            ]
            
            start_time = time.time()
            
            # 注意：由于MCP连接限制，这里串行执行
            results = []
            for topic in topics:
                result = await self.host.search(
                    topic=topic,
                    description=f"搜索{topic}相关内容"
                )
                results.append(result)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            self.test_results["performance_tests"]["concurrent_search"] = {
                "status": "PASS",
                "total_time": total_time,
                "average_time": total_time / len(topics),
                "topics_count": len(topics)
            }
            
            logger.info(f"✅ 并发搜索测试 - 通过")
            logger.info(f"   总时间: {total_time:.2f}秒")
            logger.info(f"   平均时间: {total_time/len(topics):.2f}秒/搜索")
            
        except Exception as e:
            self.test_results["performance_tests"]["concurrent_search"] = {
                "status": "FAIL",
                "message": str(e)
            }
            logger.error(f"❌ 并发搜索测试 - 失败: {e}")
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("📊 生成测试报告")
        
        total_tests = 0
        passed_tests = 0
        
        print("\n" + "="*80)
        print("🧪 Host V3 完整流程测试报告")
        print("="*80)
        
        for category, tests in self.test_results.items():
            print(f"\n📋 {category.replace('_', ' ').title()}:")
            print("-" * 50)
            
            for test_name, result in tests.items():
                total_tests += 1
                status = result["status"]
                
                if status == "PASS":
                    passed_tests += 1
                    print(f"  ✅ {test_name}: {status}")
                elif status == "EXPECTED_ERROR":
                    passed_tests += 1
                    print(f"  ⚠️ {test_name}: {status}")
                else:
                    print(f"  ❌ {test_name}: {status}")
                
                if "message" in result:
                    print(f"     {result['message']}")
                if "execution_time" in result:
                    print(f"     执行时间: {result['execution_time']:.2f}秒")
                if "turns" in result:
                    print(f"     搜索轮数: {result['turns']}")
        
        print(f"\n📊 总体统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        print("="*80)
        
        return passed_tests, total_tests

async def main():
    """主测试函数"""
    print("🚀 Host V3 完整流程测试开始")
    print("="*80)
    
    tester = HostV3CompleteTester()
    
    # 初始化
    if not await tester.setup():
        print("❌ 测试环境初始化失败")
        return
    
    # 执行所有测试
    await tester.test_basic_functionality()
    await tester.test_autonomous_search()
    await tester.test_error_handling()
    await tester.test_performance()
    
    # 生成报告
    passed, total = tester.generate_report()
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print(f"⚠️ {total-passed} 个测试失败")

if __name__ == "__main__":
    asyncio.run(main())
