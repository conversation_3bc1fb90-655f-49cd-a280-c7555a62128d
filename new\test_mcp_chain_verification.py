#!/usr/bin/env python3
"""
验证MCP链路：llm_search_host -> llm_search_client -> llm_search_server
确保Host内LLM通过Client调用Server中的工具
"""

import asyncio
import json
import logging
import os
import sys

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.llm_search_host import LLMSearchHost, create_llm_search_host

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_chain_step_by_step():
    """逐步测试MCP链路的每个环节"""
    print("🔗 开始逐步验证MCP链路")
    
    # 1. 测试Host创建
    print("\n1️⃣ 测试Host创建...")
    try:
        host = create_llm_search_host()
        print("✅ LLMSearchHost创建成功")
        print(f"   模型: {host.model}")
        print(f"   推理类型: {host.infer_type}")
    except Exception as e:
        print(f"❌ Host创建失败: {e}")
        return False
    
    # 2. 测试MCP客户端连接
    print("\n2️⃣ 测试MCP客户端连接...")
    try:
        client = await host._get_mcp_client()
        print("✅ MCP客户端连接成功")
        
        # 列出可用工具
        tools = await client.list_tools()
        tool_names = [tool['name'] for tool in tools]
        print(f"   可用工具: {tool_names}")
        
        # 验证必要工具存在
        required_tools = ['generate_search_queries', 'web_search', 'crawl_urls']
        missing_tools = [tool for tool in required_tools if tool not in tool_names]
        
        if missing_tools:
            print(f"⚠️ 缺少必要工具: {missing_tools}")
        else:
            print("✅ 所有必要工具都可用")
            
    except Exception as e:
        print(f"❌ MCP客户端连接失败: {e}")
        return False
    
    # 3. 测试单个工具调用
    print("\n3️⃣ 测试单个工具调用...")
    try:
        # 测试查询生成工具
        print("   测试 generate_search_queries...")
        result = await client.call_tool("generate_search_queries", {
            "topic": "machine learning",
            "description": "Basic ML concepts",
            "num_queries": 5
        })
        
        if result and 'queries' in result:
            queries = result['queries']
            print(f"   ✅ 生成了 {len(queries)} 个查询")
            print(f"   示例查询: {queries[0] if queries else 'None'}")
        else:
            print(f"   ❌ 查询生成失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用失败: {e}")
        return False
    
    # 4. 测试LLM选择和调用工具
    print("\n4️⃣ 测试LLM选择和调用工具...")
    try:
        # 这里应该测试Host内的LLM如何选择和调用工具
        # 但由于_llm_select_and_call_tool可能需要特定的实现，我们先测试基本功能
        
        available_tools = await host._get_available_tools()
        print(f"   ✅ Host可以获取可用工具: {len(available_tools)} 个")
        
        # 测试工具描述
        for tool in available_tools[:2]:  # 只显示前2个
            print(f"   - {tool['name']}: {tool.get('description', 'No description')[:50]}...")
            
    except Exception as e:
        print(f"❌ LLM工具选择测试失败: {e}")
        return False
    
    # 5. 测试完整搜索流程
    print("\n5️⃣ 测试完整搜索流程...")
    try:
        print("   开始搜索: 'neural networks'")
        results = await host.search(
            topic="neural networks",
            description="Basic concepts in neural networks",
            top_n=3
        )
        
        print(f"   ✅ 搜索完成，获得 {len(results)} 个结果")
        
        if results:
            # 验证结果格式
            first_result = results[0]
            required_fields = ['title', 'url', 'content', 'bibkey', 'abstract', 'similarity_score', 'token_count']
            missing_fields = [field for field in required_fields if field not in first_result]
            
            if not missing_fields:
                print("   ✅ 结果格式正确")
            else:
                print(f"   ⚠️ 结果格式不完整，缺少: {missing_fields}")
            
            print(f"   示例结果:")
            print(f"     标题: {first_result.get('title', 'N/A')}")
            print(f"     URL: {first_result.get('url', 'N/A')}")
            print(f"     相似度: {first_result.get('similarity_score', 'N/A')}")
        else:
            print("   ⚠️ 没有获得搜索结果")
            
    except Exception as e:
        print(f"❌ 完整搜索流程失败: {e}")
        logger.error(f"Search flow failed: {e}", exc_info=True)
        return False
    
    # 6. 清理资源
    print("\n6️⃣ 清理资源...")
    try:
        await host._cleanup_client()
        print("✅ 资源清理完成")
    except Exception as e:
        print(f"⚠️ 资源清理失败: {e}")
    
    return True

async def test_architecture_compliance():
    """测试架构合规性"""
    print("\n🏗️ 测试架构合规性...")
    
    # 1. 验证类暴露
    print("1️⃣ 验证类暴露...")
    try:
        # Host应该使用LLMSearchHost
        from src.search.llm_search_host import LLMSearchHost
        host = LLMSearchHost()
        print("✅ Host正确使用LLMSearchHost类")
        
        # Server应该暴露LLM_Search
        from src.search.llm_search_mcp_server import LLM_search
        print("✅ Server正确暴露LLM_Search类")
        
        # Client不应该直接暴露LLM_Search
        try:
            from src.search.llm_search_mcp_client import LLM_search as ClientLLMSearch
            print("❌ Client不应该暴露LLM_Search类")
            return False
        except ImportError:
            print("✅ Client正确地没有暴露LLM_Search类")
            
    except Exception as e:
        print(f"❌ 类暴露验证失败: {e}")
        return False
    
    # 2. 验证Host不直接连接底层工具
    print("\n2️⃣ 验证Host不直接连接底层工具...")
    try:
        host = create_llm_search_host()
        
        # 检查Host是否有直接的搜索方法（应该没有）
        forbidden_methods = ['_direct_comprehensive_search', '_execute_full_search_pipeline']
        for method in forbidden_methods:
            if hasattr(host, method):
                print(f"❌ Host不应该有直接搜索方法: {method}")
                return False
        
        print("✅ Host正确地没有直接连接底层工具的方法")
        
    except Exception as e:
        print(f"❌ Host架构验证失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始MCP链路完整验证\n")
    
    # 测试结果
    results = {}
    
    # 1. 逐步验证MCP链路
    results['mcp_chain'] = await test_mcp_chain_step_by_step()
    
    # 2. 验证架构合规性
    results['architecture'] = await test_architecture_compliance()
    
    # 总结结果
    print("\n📊 验证结果总结:")
    print("=" * 50)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    all_passed = all(results.values())
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有验证通过！MCP链路架构正确")
        print("\n✅ 确认:")
        print("  - Host内LLM通过Client调用Server中的工具")
        print("  - 没有直接连接到底层LLM_Search工具包")
        print("  - 类暴露符合架构要求")
    else:
        print("⚠️ 部分验证失败，请检查相关组件")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
