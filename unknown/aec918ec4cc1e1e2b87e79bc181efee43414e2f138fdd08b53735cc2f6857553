{"memory_settings": {"enabled": true, "memory_file": "memory.json", "max_history_per_model": 1000, "max_context_messages": 10, "auto_backup": true, "backup_interval_hours": 24}, "model_specific_settings": {"gemini-2.0-flash-thinking-exp-01-21": {"max_context_messages": 12, "max_history": 1500, "enable_context_compression": true}, "gpt-4": {"max_context_messages": 8, "max_history": 800, "enable_context_compression": false}, "claude-3-sonnet": {"max_context_messages": 10, "max_history": 1000, "enable_context_compression": true}}, "context_management": {"context_window_strategy": "sliding", "context_relevance_threshold": 0.7, "auto_summarize_old_context": true, "summarize_threshold_messages": 50}, "storage_optimization": {"compress_old_conversations": true, "compression_age_days": 7, "max_file_size_mb": 100, "auto_cleanup_enabled": true, "cleanup_age_days": 30}, "privacy_settings": {"anonymize_sensitive_data": false, "exclude_patterns": ["password", "api_key", "secret", "token"], "encrypt_storage": false}, "performance_settings": {"async_save": true, "batch_save_interval_seconds": 5, "memory_cache_size": 100, "lazy_load_history": true}}