#!/usr/bin/env python3
"""
测试对话历史存储功能
验证RequestWrapper与MemoryManager的集成
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from request.wrapper import RequestWrapper
from src.utils.memory_manager import get_memory_manager

def test_basic_memory_functionality():
    """测试基本的对话历史功能"""
    print("🧪 测试1: 基本对话历史功能")
    print("-" * 50)
    
    try:
        # 创建RequestWrapper实例，启用内存功能
        wrapper = RequestWrapper(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            use_memory=True,
            max_context_messages=6
        )
        
        print("✓ 创建RequestWrapper实例成功")
        
        # 模拟几次对话
        conversations = [
            "你好，我想了解机器学习的基础概念",
            "什么是监督学习？",
            "能给我举个分类算法的例子吗？",
            "决策树算法的优缺点是什么？"
        ]
        
        print(f"✓ 准备进行 {len(conversations)} 次对话测试")
        
        for i, message in enumerate(conversations, 1):
            print(f"\n对话 {i}: {message[:30]}...")
            
            try:
                # 这里我们模拟LLM响应，因为可能没有真实的API密钥
                # 在实际使用中，这会调用真实的LLM API
                response = f"这是对'{message}'的模拟响应 {i}"
                
                # 手动添加到内存（模拟completion方法的行为）
                from src.utils.memory_manager import add_conversation_to_memory
                add_conversation_to_memory(
                    model=wrapper.model,
                    messages=[{"role": "user", "content": message}],
                    response=response,
                    metadata={"test_conversation": i, "timestamp": time.time()}
                )
                
                print(f"  ✓ 对话 {i} 已保存到内存")
                
            except Exception as e:
                print(f"  ❌ 对话 {i} 失败: {e}")
        
        # 检查对话历史
        history = wrapper.get_conversation_history()
        print(f"\n✓ 成功保存了 {len(history)} 条对话记录")
        
        # 显示最近的对话
        if history:
            print("\n最近的对话记录:")
            for i, conv in enumerate(history[-2:], 1):
                print(f"  {i}. 时间: {conv['timestamp']}")
                print(f"     用户: {conv['messages'][0]['content'][:50]}...")
                print(f"     助手: {conv['response'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_memory_persistence():
    """测试内存持久化功能"""
    print("\n🧪 测试2: 内存持久化功能")
    print("-" * 50)
    
    try:
        # 检查memory.json文件是否存在
        memory_file = "memory.json"
        if os.path.exists(memory_file):
            print(f"✓ 找到内存文件: {memory_file}")
            
            # 读取并显示内容
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            print(f"✓ 内存文件包含 {len(memory_data)} 个模型的对话历史")
            
            for model, conversations in memory_data.items():
                print(f"  模型 {model}: {len(conversations)} 条对话")
                if conversations:
                    latest = conversations[-1]
                    print(f"    最新对话时间: {latest['timestamp']}")
        else:
            print("⚠️  内存文件不存在，可能是首次运行")
        
        return True
        
    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")
        return False

def test_multiple_models():
    """测试多模型对话历史分离"""
    print("\n🧪 测试3: 多模型对话历史分离")
    print("-" * 50)
    
    try:
        models = [
            "gemini-2.0-flash-thinking-exp-01-21",
            "gpt-4",
            "claude-3-sonnet"
        ]
        
        memory_manager = get_memory_manager()
        
        # 为每个模型添加测试对话
        for model in models:
            memory_manager.add_conversation(
                model=model,
                messages=[{"role": "user", "content": f"测试消息给{model}"}],
                response=f"来自{model}的响应",
                metadata={"test_model": model}
            )
            print(f"✓ 为模型 {model} 添加了测试对话")
        
        # 检查每个模型的对话历史
        all_models = memory_manager.get_all_models()
        print(f"\n✓ 系统中共有 {len(all_models)} 个模型有对话历史")
        
        for model in all_models:
            history = memory_manager.get_conversation_history(model)
            print(f"  {model}: {len(history)} 条对话")
        
        return True
        
    except Exception as e:
        print(f"❌ 多模型测试失败: {e}")
        return False

def test_memory_statistics():
    """测试内存统计功能"""
    print("\n🧪 测试4: 内存统计功能")
    print("-" * 50)
    
    try:
        wrapper = RequestWrapper(
            model="gemini-2.0-flash-thinking-exp-01-21",
            use_memory=True
        )
        
        # 获取统计信息
        stats = wrapper.get_memory_statistics()
        print("✓ 获取内存统计信息成功")
        
        if stats:
            print("\n内存统计:")
            for model, model_stats in stats.items():
                print(f"  模型: {model}")
                print(f"    总对话数: {model_stats['total_conversations']}")
                print(f"    首次对话: {model_stats['first_conversation']}")
                print(f"    最新对话: {model_stats['last_conversation']}")
        else:
            print("  暂无统计数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 统计功能测试失败: {e}")
        return False

def test_context_retrieval():
    """测试上下文检索功能"""

    
    try:
        from src.utils.memory_manager import get_conversation_context
        
        model = "gemini-2.0-flash-thinking-exp-01-21"
        context = get_conversation_context(model, max_messages=4)
        
        print(f"获取模型 {model} 的上下文成功")
        print(f"上下文包含 {len(context)} 条消息")
        
        if context:
            print("\n上下文消息:")
            for i, msg in enumerate(context[-2:], 1):  # 只显示最后2条
                print(f"  {i}. {msg['role']}: {msg['content'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文检索测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据")
    print("-" * 50)
    
    try:
        # 可选择是否删除测试生成的memory.json文件
        memory_file = "memory.json"
        if os.path.exists(memory_file):
            choice = input("是否删除测试生成的memory.json文件? (y/N): ").lower()
            if choice == 'y':
                os.remove(memory_file)
                print("✓ 已删除memory.json文件")
            else:
                print("✓ 保留memory.json文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 对话历史存储功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("基本功能", test_basic_memory_functionality),
        ("持久化", test_memory_persistence),
        ("多模型分离", test_multiple_models),
        ("统计功能", test_memory_statistics),
        ("上下文检索", test_context_retrieval)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))


if __name__ == "__main__":
    main()
