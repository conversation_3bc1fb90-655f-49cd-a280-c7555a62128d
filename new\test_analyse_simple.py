#!/usr/bin/env python3
"""
简化的analyse完整流程测试
确保analyse全流程跑通并输出详细日志
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.analyse import AnalyseInterface

# 配置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def test_analyse_complete_workflow():
    """测试analyse完整工作流程"""
    logger.info("🚀 开始测试analyse完整工作流程")
    logger.info("=" * 80)
    
    try:
        # 初始化analyse
        logger.info("🔧 初始化AnalyseInterface...")
        analyser = AnalyseInterface(
            base_dir="test_analyse",
            max_interaction_rounds=1,  # 简化交互轮数
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        logger.info("✅ AnalyseInterface初始化成功")
        
        # 测试参数
        topic = "机器学习基础"
        description = "机器学习的基本概念、算法和应用"
        top_n = 3
        
        logger.info(f"📋 测试参数:")
        logger.info(f"   主题: {topic}")
        logger.info(f"   描述: {description}")
        logger.info(f"   目标文献数: {top_n}")
        logger.info("=" * 80)
        
        # 执行完整分析流程
        logger.info("🎯 开始执行完整analyse流程...")
        results = await analyser.analyse(topic, description, top_n)
        
        # 输出结果
        logger.info("=" * 80)
        logger.info("📊 分析结果:")
        logger.info(f"   结果类型: {type(results)}")
        
        if isinstance(results, list):
            logger.info(f"   结果数量: {len(results)}")
            for i, result in enumerate(results):
                logger.info(f"   结果 {i+1}: {type(result)}")
                if isinstance(result, dict):
                    logger.info(f"     字段: {list(result.keys())}")
                    if 'title' in result:
                        logger.info(f"     标题: {result.get('title', 'N/A')[:100]}...")
                    if 'url' in result:
                        logger.info(f"     URL: {result.get('url', 'N/A')}")
        elif isinstance(results, dict):
            logger.info(f"   结果字段: {list(results.keys())}")
            for key, value in results.items():
                if isinstance(value, (str, int, float)):
                    logger.info(f"   {key}: {value}")
                elif isinstance(value, list):
                    logger.info(f"   {key}: {len(value)} 项")
                else:
                    logger.info(f"   {key}: {type(value)}")
        
        logger.info("=" * 80)
        logger.info("✅ analyse完整流程测试成功!")
        return True
        
    except Exception as e:
        logger.error("=" * 80)
        logger.error(f"❌ analyse流程测试失败: {e}")
        import traceback
        logger.error(f"📋 错误详情:\n{traceback.format_exc()}")
        logger.error("=" * 80)
        return False

async def main():
    """主函数"""
    logger.info("🎯 开始analyse完整流程测试")
    logger.info("目标: 确保analyse全流程跑通并输出详细日志")
    logger.info("=" * 80)
    
    success = await test_analyse_complete_workflow()
    
    if success:
        logger.info("🎉 所有测试通过!")
        return True
    else:
        logger.error("💥 测试失败!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
