import asyncio
import json
import logging
import os
import sys
from typing import List, Dict, Any, Optional

from .llm_search_mcp_client import MC<PERSON>lient, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_model_config():
    """加载模型配置"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'model_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"Model config loaded from: {config_path}")
        return config
    except Exception as e:
        logger.warning(f"Failed to load model config, using defaults: {e}")
        return {
            "search": {
                "host_llm": {
                    "model": "gemini-2.0-flash-thinking-exp-01-21",
                    "infer_type": "OpenAI"
                }
            }
        }

MODEL_CONFIG = load_model_config()

class LLMSearchHostV3:
    """
    LLM搜索主机 V3版本
    
    特点：
    1. 不暴露Server的具体工具
    2. 实现多轮对话模式，让LLM自主选择工具
    3. 每次工具调用后断开连接，避免连接问题
    4. 简化的接口设计
    """

    def __init__(
        self,
        model: str = None,
        infer_type: str = None,
        use_memory: bool = True,
        max_context_messages: int = 10,
    ):
        # 从配置文件读取默认模型配置
        if model is None or infer_type is None:
            host_config = MODEL_CONFIG.get("search", {}).get("host_llm", {})
            model = model or host_config.get("model", "gemini-2.0-flash-thinking-exp-01-21")
            infer_type = infer_type or host_config.get("infer_type", "OpenAI")

        self.model = model
        self.infer_type = infer_type
        self.use_memory = use_memory
        
        # 初始化LLM请求包装器
        self.request_wrapper = RequestWrapper(
            model=model,
            infer_type=infer_type,
            use_memory=use_memory,
            max_context_messages=max_context_messages
        )
        
        # 搜索状态
        self.search_state = {
            "topic": "",
            "description": "",
            "queries": [],
            "urls": [],
            "final_results": [],
            "completed_steps": [],
            "current_step": "start"
        }

        logger.info(f"LLMSearchHostV3 initialized with model: {model}, infer_type: {infer_type}")

    async def _create_mcp_client(self) -> MCPClient:
        """创建新的MCP客户端连接"""
        try:
            client = await create_mcp_client_from_config()
            logger.debug("Created new MCP client connection")
            return client
        except Exception as e:
            logger.error(f"Failed to create MCP client: {e}")
            raise

    async def _get_available_tools(self) -> List[dict]:
        """获取可用工具列表"""
        client = await self._create_mcp_client()
        try:
            tools = await client.list_tools()
            return tools
        finally:
            await client.disconnect()

    def _create_search_planning_prompt(self, topic: str, description: str, available_tools: List[dict]) -> str:
        """创建搜索规划提示词 - 基于MCP最佳实践"""
        tools_info = self._format_available_tools(available_tools)

        return f"""你是一个智能文献搜索助手，使用Model Context Protocol (MCP)工具来完成搜索任务。

## 搜索任务
主题：{topic}
描述：{description}

## 可用MCP工具
{tools_info}

## 任务要求
基于MCP多轮工具选择模式，你需要：
1. 分析搜索任务的具体需求
2. 制定分步骤的搜索策略
3. 选择第一个要使用的工具
4. 提供正确的工具调用参数

## 工具选择策略
- 通常先使用 generate_search_queries 生成搜索关键词
- 然后使用 web_search 执行搜索
- 接着使用 crawl_urls 获取详细内容
- 最后使用 analyze_relevance 筛选结果

## 响应格式
请严格按照以下JSON格式返回第一步的工具选择：
{{
    "task_understanding": "对搜索任务的理解",
    "search_strategy": "整体搜索策略概述",
    "first_tool": "第一个要使用的工具名称",
    "tool_arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "选择这个工具的理由",
    "expected_outcome": "预期的执行结果"
}}"""

    def _format_available_tools(self, tools: List[dict]) -> str:
        """格式化可用工具信息"""
        formatted = []
        for tool in tools:
            name = tool.get('name', 'unknown')
            description = tool.get('description', '无描述')
            schema = tool.get('inputSchema', {})

            # 格式化参数信息
            params_info = self._format_tool_schema(schema)

            formatted.append(f"**{name}**\n  描述: {description}\n  参数: {params_info}")

        return "\n\n".join(formatted)

    def _format_tool_schema(self, schema: dict) -> str:
        """格式化工具schema为可读文本"""
        if not schema or 'properties' not in schema:
            return "无特定参数要求"

        properties = schema['properties']
        required = schema.get('required', [])

        formatted = []
        for prop, details in properties.items():
            prop_type = details.get('type', 'unknown')
            description = details.get('description', '')
            is_required = prop in required
            req_marker = " (必需)" if is_required else " (可选)"
            formatted.append(f"{prop}: {prop_type}{req_marker} - {description}")

        return "; ".join(formatted)

    def _create_next_step_prompt(self, current_results: dict, available_tools: List[dict]) -> str:
        """创建下一步行动提示词 - 基于MCP最佳实践"""
        state_summary = f"""
## 当前搜索状态
- 主题：{self.search_state['topic']}
- 已完成步骤：{', '.join(self.search_state['completed_steps'])}
- 当前步骤：{self.search_state['current_step']}
- 已生成查询数：{len(self.search_state['queries'])}
- 已获取URL数：{len(self.search_state['urls'])}
- 最终结果数：{len(self.search_state['final_results'])}

## 最近操作结果
{json.dumps(current_results, ensure_ascii=False, indent=2)[:800]}
"""

        tools_info = self._format_available_tools(available_tools)

        return f"""你是一个智能文献搜索助手，正在使用MCP工具执行多轮搜索任务。

{state_summary}

## 可用MCP工具
{tools_info}

## 决策要求
基于当前搜索进展和最近的操作结果，请决定下一步行动：

1. **评估当前进展**：分析已完成的工作和获得的结果
2. **判断完成状态**：确定搜索是否已经完成或需要继续
3. **选择下一步工具**：如果需要继续，选择最合适的工具
4. **提供调用参数**：为选定的工具提供正确的参数

## 完成条件
你有完全的自主权决定何时完成搜索任务。搜索可以结束的情况：
- 已获得足够数量的高质量、相关的学术文献（通常5-20个）
- 搜索结果开始重复或质量明显下降
- 已覆盖主题的主要研究方向和关键论文
- 认为当前结果已能满足用户的研究需求
- 已执行了完整的搜索流程（查询生成→网络搜索→内容爬取）

**重要**：你可以自主决定最多进行10轮对话，但应该在获得满意结果时主动结束，无需等到轮数上限。

## 响应格式
请严格按照以下JSON格式返回：
{{
    "continue_search": true/false,
    "completion_reason": "如果完成搜索，说明原因",
    "next_tool": "下一个工具名称（如果继续搜索）",
    "tool_arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "决策理由和预期结果",
    "progress_assessment": "对当前进展的评估"
}}"""

    async def _llm_plan_search(self, topic: str, description: str) -> dict:
        """让LLM规划搜索策略并选择第一个工具"""
        # 获取可用工具
        available_tools = await self._get_available_tools()

        prompt = self._create_search_planning_prompt(topic, description, available_tools)
        response = await self.request_wrapper.async_request(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=1500
        )

        # 解析JSON响应
        try:
            import json
            # 尝试提取JSON部分
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
            else:
                json_str = response

            parsed = json.loads(json_str)
            return parsed
        except Exception as e:
            logger.warning(f"Failed to parse LLM planning response as JSON: {e}")
            # 回退到默认策略
            return {
                "task_understanding": "搜索任务解析失败",
                "search_strategy": "使用默认策略",
                "first_tool": "generate_search_queries",
                "tool_arguments": {"topic": topic, "description": description},
                "reasoning": "JSON解析失败，使用默认工具",
                "expected_outcome": "生成搜索查询"
            }

    async def _llm_decide_next_step(self, current_results: dict) -> dict:
        """让LLM决定下一步行动 - 基于MCP最佳实践"""
        # 获取可用工具
        available_tools = await self._get_available_tools()

        prompt = self._create_next_step_prompt(current_results, available_tools)
        response = await self.request_wrapper.async_request(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=1000
        )

        # 解析JSON响应
        try:
            import json
            # 尝试提取JSON部分
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            elif "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
            else:
                json_str = response

            parsed = json.loads(json_str)
            return parsed
        except Exception as e:
            logger.warning(f"Failed to parse LLM decision response as JSON: {e}")
            # 回退策略：基于当前状态决定
            if len(self.search_state['queries']) == 0:
                return {
                    "continue_search": True,
                    "next_tool": "generate_search_queries",
                    "tool_arguments": {"topic": self.search_state['topic']},
                    "reasoning": "JSON解析失败，需要生成查询",
                    "progress_assessment": "开始搜索流程"
                }
            elif len(self.search_state['urls']) == 0:
                return {
                    "continue_search": True,
                    "next_tool": "web_search",
                    "tool_arguments": {"queries": self.search_state['queries'][:3]},
                    "reasoning": "JSON解析失败，需要执行搜索",
                    "progress_assessment": "执行网络搜索"
                }
            else:
                return {
                    "continue_search": False,
                    "completion_reason": "JSON解析失败，但已有基本结果",
                    "reasoning": "解析失败，提前结束",
                    "progress_assessment": "异常结束"
                }

    async def _execute_tool(self, tool_name: str, arguments: dict) -> dict:
        """执行指定工具"""
        client = await self._create_mcp_client()
        try:
            logger.info(f"Executing tool: {tool_name}")
            # 添加超时处理
            # 取消超时限制，允许工具执行较长时间
            result = await client.call_tool(tool_name, arguments)
            return result
        except asyncio.TimeoutError:
            logger.error(f"Tool {tool_name} execution timeout")
            return {"error": "timeout", "tool": tool_name}
        except Exception as e:
            logger.error(f"Tool {tool_name} execution failed: {e}")
            return {"error": str(e), "tool": tool_name}
        finally:
            try:
                await client.disconnect()
            except Exception as e:
                logger.warning(f"Error disconnecting client: {e}")

    async def search(self, topic: str, description: str = "", top_n: int = 20) -> Dict[str, Any]:
        """
        执行智能搜索
        
        Args:
            topic: 搜索主题
            description: 主题描述
            top_n: 目标结果数量
            
        Returns:
            搜索结果字典
        """
        logger.info(f"🚀 开始智能搜索主题: '{topic}'")
        
        # 初始化搜索状态
        self.search_state = {
            "topic": topic,
            "description": description,
            "queries": [],
            "urls": [],
            "final_results": [],
            "completed_steps": [],
            "current_step": "planning"
        }
        
        try:
            # 第一步：让LLM规划搜索策略并选择第一个工具
            logger.info("📋 步骤1: LLM搜索策略规划和首个工具选择")
            planning_result = await self._llm_plan_search(topic, description)
            logger.info(f"搜索策略：{planning_result}")

            # 执行第一个工具
            first_tool = planning_result.get("first_tool")
            first_args = planning_result.get("tool_arguments", {})

            if first_tool:
                logger.info(f"🔧 执行首个工具: {first_tool}")
                first_result = await self._execute_tool(first_tool, first_args)
                self._update_search_state(first_tool, first_result)
                current_results = first_result
            else:
                logger.warning("⚠️ 规划中未指定首个工具，使用默认")
                current_results = {"planning": planning_result}

            # 开始多轮搜索循环，让LLM自主决定何时结束
            max_rounds = 10  # 最大轮数限制，防止无限循环
            current_round = 1 if first_tool else 0  # 如果执行了首个工具，从第2轮开始

            while current_round < max_rounds:
                current_round += 1
                logger.info(f"🔄 搜索轮次 {current_round}/{max_rounds}")

                # 让LLM基于当前结果决定下一步
                decision = await self._llm_decide_next_step(current_results)
                logger.info(f"LLM决策：{decision}")

                # 优先检查LLM是否决定结束搜索
                if not decision.get("continue_search", True):
                    completion_reason = decision.get("completion_reason", "LLM决定任务已完成")
                    logger.info(f"✅ LLM自主决定搜索完成: {completion_reason}")
                    break

                next_tool = decision.get("next_tool")
                next_args = decision.get("tool_arguments", {})

                if not next_tool:
                    logger.warning("⚠️ LLM未指定下一步工具，使用默认流程")
                    # 使用默认流程
                    if not self.search_state["queries"]:
                        next_tool = "generate_search_queries"
                        next_args = {"topic": topic, "description": description}
                    elif not self.search_state["urls"]:
                        next_tool = "web_search"
                        next_args = {"queries": self.search_state["queries"][:3]}
                    elif not self.search_state["final_results"]:
                        next_tool = "crawl_urls"
                        next_args = {"url_list": self.search_state["urls"][:top_n]}
                    else:
                        logger.info("✅ 默认流程完成，停止搜索")
                        break

                # 执行选定的工具
                try:
                    logger.info(f"🔧 执行工具: {next_tool} with args: {next_args}")
                    tool_result = await self._execute_tool(next_tool, next_args)
                    current_results = tool_result

                    # 更新搜索状态
                    self._update_search_state(next_tool, tool_result)

                    # 检查是否已获得足够结果
                    if len(self.search_state["final_results"]) >= top_n:
                        logger.info(f"✅ 已获得足够结果 ({len(self.search_state['final_results'])})")
                        break

                except Exception as e:
                    logger.error(f"❌ 工具执行失败: {e}")
                    break
            
            # 返回最终结果
            final_result = {
                "success": True,
                "topic": topic,
                "description": description,
                "planning_result": planning_result,
                "queries": self.search_state["queries"],
                "urls": self.search_state["urls"],
                "final_results": self.search_state["final_results"],
                "completed_steps": self.search_state["completed_steps"],
                "total_rounds": current_round,
                "query_count": len(self.search_state["queries"]),
                "url_count": len(self.search_state["urls"]),
                "result_count": len(self.search_state["final_results"]),
                "search_state": self.search_state
            }

            logger.info(f"🎉 搜索完成! 共{current_round}轮，获得{len(self.search_state['final_results'])}个结果")
            return final_result
            
        except Exception as e:
            logger.error(f"❌ 搜索失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {}



    def _update_search_state(self, tool_name: str, result: dict):
        """更新搜索状态"""
        self.search_state["completed_steps"].append(tool_name)

        if tool_name == "generate_search_queries":
            self.search_state["queries"] = result.get("queries", [])
        elif tool_name == "web_search":
            self.search_state["urls"] = result.get("urls", [])
        elif tool_name == "crawl_urls":
            # crawl_urls返回的是crawled_data字段
            crawled_data = result.get("crawled_data", [])
            self.search_state["final_results"] = crawled_data
        elif tool_name == "analyze_relevance":
            analyzed_urls = result.get("analyzed_urls", [])
            self.search_state["urls"] = analyzed_urls

def create_llm_search_host_v3(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI"
) -> LLMSearchHostV3:
    """创建LLM搜索主机V3实例"""
    return LLMSearchHostV3(model=model, infer_type=infer_type)
