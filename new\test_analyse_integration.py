#!/usr/bin/env python3
"""
测试analyse模块与llm_search_host的完整集成
验证整体流程是否正确，以及host能否返回正确结果
"""

import asyncio
import json
import logging
import os
import sys
from unittest.mock import patch, MagicMock

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.analyse import AnalyseInterface, analyse

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def mock_user_input(responses):
    """模拟用户输入的生成器"""
    for response in responses:
        yield response

async def test_analyse_interface_initialization():
    """测试AnalyseInterface初始化"""
    print("🔧 测试AnalyseInterface初始化")
    print("=" * 50)
    
    try:
        # 测试默认初始化
        analyser = AnalyseInterface(
            base_dir="new/test_analyse",
            max_interaction_rounds=2,
            llm_model="gemini-2.0-flash-thinking-exp-01-21",
            llm_infer_type="OpenAI"
        )
        
        print(f"✅ AnalyseInterface初始化成功")
        print(f"   - 基础目录: {analyser.base_dir}")
        print(f"   - 最大交互轮数: {analyser.max_interaction_rounds}")
        print(f"   - LLM模型: {analyser.llm_model}")
        print(f"   - LLM推理类型: {analyser.llm_infer_type}")
        print(f"   - LLM搜索引擎类型: {type(analyser.llm_search).__name__}")
        
        # 验证组件是否正确初始化
        assert hasattr(analyser, 'llm_wrapper'), "LLM wrapper未初始化"
        assert hasattr(analyser, 'llm_search'), "LLM search未初始化"
        assert hasattr(analyser, 'config'), "配置未加载"
        assert hasattr(analyser, 'prompts'), "提示模板未加载"
        
        print("✅ 所有组件初始化验证通过")
        return True
        
    except Exception as e:
        print(f"❌ AnalyseInterface初始化失败: {e}")
        logger.error(f"Initialization test failed: {e}", exc_info=True)
        return False

async def test_topic_expansion():
    """测试主题扩展功能"""
    print("\n📝 测试主题扩展功能")
    print("=" * 50)
    
    try:
        analyser = AnalyseInterface(
            base_dir="new/test_analyse",
            max_interaction_rounds=1
        )
        
        # 测试主题扩展
        topic = "机器学习"
        description = "机器学习基础概念和算法"
        
        print(f"原始主题: {topic}")
        print(f"原始描述: {description}")
        
        expanded_topic = analyser._expand_topic_with_llm(topic, description)
        
        print(f"✅ 主题扩展成功")
        print(f"   扩展结果类型: {type(expanded_topic)}")
        
        if isinstance(expanded_topic, dict):
            print(f"   扩展结果字段: {list(expanded_topic.keys())}")
            if 'description' in expanded_topic:
                desc = expanded_topic['description']
                print(f"   扩展描述长度: {len(desc)} 字符")
                print(f"   扩展描述预览: {desc[:100]}...")
        else:
            print(f"   扩展结果: {str(expanded_topic)[:200]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ 主题扩展测试失败: {e}")
        logger.error(f"Topic expansion test failed: {e}", exc_info=True)
        return False

async def test_literature_retrieval():
    """测试文献检索功能"""
    print("\n📚 测试文献检索功能")
    print("=" * 50)
    
    try:
        analyser = AnalyseInterface(
            base_dir="new/test_analyse",
            max_interaction_rounds=1
        )
        
        # 模拟精炼后的主题
        refined_topic = {
            "core_concept": "深度学习",
            "description": "深度学习是机器学习的一个分支，使用多层神经网络来学习数据的表示。研究重点包括卷积神经网络、循环神经网络、生成对抗网络等架构，以及在计算机视觉、自然语言处理、语音识别等领域的应用。"
        }
        
        print(f"测试主题: {refined_topic['core_concept']}")
        print(f"描述长度: {len(refined_topic['description'])} 字符")
        
        # 测试文献检索
        literature_results = await analyser._retrieve_literature(refined_topic, top_n=5)
        
        print(f"✅ 文献检索完成")
        print(f"   结果类型: {type(literature_results)}")

        if isinstance(literature_results, list):
            print(f"   检索结果数量: {len(literature_results)}")
            if literature_results:
                print(f"   第一个结果类型: {type(literature_results[0])}")
                if isinstance(literature_results[0], dict):
                    print(f"   第一个结果字段: {list(literature_results[0].keys())}")
                    # 检查是否是查询生成结果
                    if literature_results[0].get('type') == 'search_initiated':
                        print(f"   状态: {literature_results[0].get('status')}")
                        print(f"   消息: {literature_results[0].get('message')}")
        else:
            print(f"   意外的结果格式: {literature_results}")

        return True
        
    except Exception as e:
        print(f"❌ 文献检索测试失败: {e}")
        logger.error(f"Literature retrieval test failed: {e}", exc_info=True)
        return False

async def test_full_analyse_workflow():
    """测试完整的analyse工作流程（模拟用户交互）"""
    print("\n🔄 测试完整analyse工作流程")
    print("=" * 50)
    
    try:
        # 模拟用户输入
        mock_inputs = [
            "y",  # 满意当前分析
        ]
        
        with patch('builtins.input', side_effect=mock_inputs):
            analyser = AnalyseInterface(
                base_dir="new/test_analyse",
                max_interaction_rounds=1  # 减少交互轮数以简化测试
            )
            
            topic = "计算机视觉"
            description = "计算机视觉技术和应用"
            top_n = 3
            
            print(f"测试主题: {topic}")
            print(f"测试描述: {description}")
            print(f"目标文献数: {top_n}")
            
            # 执行完整分析
            results = await analyser.analyse(topic, description, top_n)
            
            print(f"✅ 完整工作流程执行成功")
            print(f"   最终结果数量: {len(results) if results else 0}")
            print(f"   结果类型: {type(results)}")
            
            if results:
                print(f"   第一个结果预览: {str(results[0])[:100]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {e}")
        logger.error(f"Full workflow test failed: {e}", exc_info=True)
        return False

async def test_standalone_analyse_function():
    """测试独立的analyse函数"""
    print("\n🎯 测试独立analyse函数")
    print("=" * 50)
    
    try:
        # 模拟用户输入
        mock_inputs = [
            "y",  # 满意当前分析
        ]
        
        with patch('builtins.input', side_effect=mock_inputs):
            topic = "自然语言处理"
            description = "NLP技术和应用研究"
            top_n = 3
            max_interaction_rounds = 1
            
            print(f"测试主题: {topic}")
            print(f"测试描述: {description}")
            print(f"目标文献数: {top_n}")
            print(f"最大交互轮数: {max_interaction_rounds}")
            
            # 调用独立函数
            results = await analyse(topic, description, top_n, max_interaction_rounds)
            
            print(f"✅ 独立函数调用成功")
            print(f"   结果数量: {len(results) if results else 0}")
            print(f"   结果类型: {type(results)}")
            
            if results:
                print(f"   结果预览: {str(results[0])[:100]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ 独立函数测试失败: {e}")
        logger.error(f"Standalone function test failed: {e}", exc_info=True)
        return False

async def test_host_integration():
    """测试与llm_search_host的集成"""
    print("\n🔗 测试与llm_search_host的集成")
    print("=" * 50)
    
    try:
        analyser = AnalyseInterface(
            base_dir="new/test_analyse",
            max_interaction_rounds=1
        )
        
        # 直接测试host的search方法
        print("直接测试host的search方法...")
        
        search_result = await analyser.llm_search.search(
            topic="人工智能",
            description="人工智能基础理论和应用",
            top_n=3
        )
        
        print(f"✅ Host集成测试成功")
        print(f"   Host搜索结果类型: {type(search_result)}")

        if isinstance(search_result, list):
            print(f"   Host搜索结果数量: {len(search_result)}")
            if search_result:
                print(f"   第一个结果类型: {type(search_result[0])}")
                if isinstance(search_result[0], dict):
                    print(f"   第一个结果字段: {list(search_result[0].keys())}")
        elif isinstance(search_result, dict):
            print(f"   Host搜索结果字段: {list(search_result.keys())}")
            # 检查是否有queries字段
            if 'queries' in search_result and isinstance(search_result['queries'], list):
                print(f"   生成的查询数量: {len(search_result['queries'])}")
        else:
            print(f"   Host搜索结果内容: {str(search_result)[:100]}...")
        
        # 测试host的其他方法
        print("\n测试host的工具获取功能...")
        available_tools = await analyser.llm_search._get_available_tools()
        print(f"   可用工具数量: {len(available_tools)}")
        print(f"   工具列表: {[tool['name'] for tool in available_tools]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Host集成测试失败: {e}")
        logger.error(f"Host integration test failed: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试analyse模块与llm_search_host的完整集成")
    print("验证整体流程正确性和host返回结果")
    print("=" * 80)
    
    test_results = {}
    
    # 1. 测试初始化
    test_results['initialization'] = await test_analyse_interface_initialization()
    
    # 2. 测试主题扩展
    test_results['topic_expansion'] = await test_topic_expansion()
    
    # 3. 测试文献检索
    test_results['literature_retrieval'] = await test_literature_retrieval()
    
    # 4. 测试Host集成
    test_results['host_integration'] = await test_host_integration()
    
    # 5. 测试完整工作流程
    test_results['full_workflow'] = await test_full_analyse_workflow()
    
    # 6. 测试独立函数
    test_results['standalone_function'] = await test_standalone_analyse_function()
    
    # 总结结果
    print("\n📊 测试结果总结:")
    print("=" * 60)
    
    for test_name, success in test_results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:25} : {status}")
    
    all_passed = all(test_results.values())
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！")
        print("\n✅ 验证确认:")
        print("  - AnalyseInterface初始化正确")
        print("  - 主题扩展功能正常")
        print("  - 文献检索功能正常")
        print("  - 与llm_search_host集成正确")
        print("  - 完整工作流程运行正常")
        print("  - 独立analyse函数工作正常")
        print("  - Host能够返回正确的结构化结果")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
