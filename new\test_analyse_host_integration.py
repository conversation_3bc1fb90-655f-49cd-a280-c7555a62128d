#!/usr/bin/env python3
"""
测试analyse.py与host的集成
验证analyse能正确调用host进行搜索
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.analyse import LiteratureAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_analyse_host_integration():
    """测试analyse与host的集成"""
    print("🧪 测试analyse与host的集成")
    print("="*60)
    
    try:
        # 1. 初始化LiteratureAnalyzer
        print("📋 步骤1: 初始化LiteratureAnalyzer...")
        analyzer = LiteratureAnalyzer()
        print("✅ LiteratureAnalyzer初始化成功")
        
        # 2. 检查host是否正确初始化
        print("📋 步骤2: 检查host初始化...")
        assert hasattr(analyzer, 'llm_search'), "analyzer没有llm_search属性"
        assert analyzer.llm_search is not None, "llm_search未正确初始化"
        print("✅ Host初始化检查通过")
        
        # 3. 测试简单的搜索调用
        print("📋 步骤3: 测试简单搜索...")
        
        # 使用一个简单的主题进行测试
        topic = "artificial intelligence"
        description = "搜索人工智能基础知识"
        
        print(f"   主题: {topic}")
        print(f"   描述: {description}")
        
        # 调用analyse方法
        results = await analyzer.analyse(
            topic=topic,
            description=description,
            top_n=5  # 限制结果数量以加快测试
        )
        
        print("✅ 搜索调用成功")
        print(f"   返回结果类型: {type(results)}")
        print(f"   结果数量: {len(results) if isinstance(results, list) else 'N/A'}")
        
        # 4. 验证结果格式
        print("📋 步骤4: 验证结果格式...")
        
        if isinstance(results, list):
            print(f"✅ 返回了列表格式的结果，包含 {len(results)} 个项目")
            
            if len(results) > 0:
                first_result = results[0]
                print(f"   第一个结果类型: {type(first_result)}")
                if isinstance(first_result, dict):
                    print(f"   第一个结果的键: {list(first_result.keys())}")
                    if 'title' in first_result:
                        print(f"   第一个结果标题: {first_result['title'][:100]}...")
            else:
                print("⚠️ 返回的结果列表为空")
        else:
            print(f"⚠️ 返回的结果不是列表格式: {type(results)}")
        
        print("\n🎉 analyse与host集成测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 analyse与host集成测试开始")
    
    success = await test_analyse_host_integration()
    
    if success:
        print("\n✅ 所有测试通过！")
        print("analyse.py能够正确调用host进行搜索")
    else:
        print("\n❌ 测试失败！")
        print("需要检查analyse.py与host的集成问题")

if __name__ == "__main__":
    asyncio.run(main())
