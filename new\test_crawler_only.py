#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅测试爬虫功能，跳过SERP搜索
"""

import asyncio
import json
import sys
import os

# 添加路径以导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'search'))

try:
    from llm_search_mcp_server import _crawl_urls, SERVER_CONFIG
    print("✅ 成功导入 llm_search_mcp_server 模块")
except ImportError as e:
    print(f"❌ 导入 llm_search_mcp_server 失败: {e}")
    sys.exit(1)

async def test_crawler_with_test_urls():
    """使用测试URL测试爬虫功能"""
    print("\n=== 测试爬虫功能（使用测试URL）===")
    
    # 使用一些可靠的测试URL
    test_urls = [
        "https://en.wikipedia.org/wiki/Machine_learning",
        "https://en.wikipedia.org/wiki/Deep_learning",
        "https://en.wikipedia.org/wiki/Artificial_neural_network"
    ]
    
    try:
        result = await _crawl_urls(
            topic="machine learning",
            url_list=test_urls,
            top_n=5,
            similarity_threshold=60,  # 降低阈值以获得更多结果
            min_length=200,
            max_length=15000
        )
        
        print(f"✅ URL爬取成功")
        print(f"总URL数: {result['total_urls']}")
        print(f"爬取结果数: {result['crawl_results']}")
        print(f"过滤结果数: {result['filtered_results']}")
        print(f"评分结果数: {result['scored_results']}")
        print(f"最终结果数: {result['final_count']}")
        print(f"处理时间: {result['processing_metadata']['total_time']:.2f}秒")
        
        if result['final_results']:
            print(f"\n爬取结果详情:")
            for i, paper in enumerate(result['final_results'], 1):
                print(f"\n{i}. 标题: {paper['title']}")
                print(f"   URL: {paper['url']}")
                print(f"   相似度评分: {paper['similarity_score']}")
                print(f"   内容长度: {paper['content_length']}")
                print(f"   内容预览: {paper['content'][:200]}...")
        
        return result
        
    except Exception as e:
        print(f"❌ URL爬取失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return None

def analyze_current_format(crawl_result):
    """分析当前的输出格式"""
    print("\n=== 分析当前输出格式 ===")
    
    if not crawl_result or not crawl_result['final_results']:
        print("❌ 没有结果可供分析")
        return
    
    try:
        sample_result = crawl_result['final_results'][0]
        
        print("当前输出格式:")
        print(json.dumps(sample_result, ensure_ascii=False, indent=2))
        
        print(f"\n当前字段列表:")
        for i, (key, value) in enumerate(sample_result.items(), 1):
            value_type = type(value).__name__
            value_preview = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
            print(f"  {i:2d}. {key:20s}: {value_type:10s} = {value_preview}")
        
        # 对比需要的字段
        print(f"\n字段兼容性分析:")
        
        # 当前有的字段
        current_fields = set(sample_result.keys())
        
        # LLMxMapReduce_V2 期望的字段
        expected_fields = {'title', 'url', 'txt', 'similarity'}
        
        # Survey 期望的字段
        survey_fields = {'title', 'url', 'txt', 'bibkey', 'abstract', 'txt_token'}
        
        print(f"当前字段: {current_fields}")
        print(f"LLMxMapReduce_V2期望: {expected_fields}")
        print(f"Survey期望: {survey_fields}")
        
        # 检查兼容性
        missing_for_llm = expected_fields - current_fields
        missing_for_survey = survey_fields - current_fields
        extra_fields = current_fields - survey_fields
        
        if missing_for_llm:
            print(f"❌ 缺少LLMxMapReduce_V2字段: {missing_for_llm}")
        else:
            print(f"✅ 包含所有LLMxMapReduce_V2字段")
            
        if missing_for_survey:
            print(f"⚠️ 缺少Survey字段: {missing_for_survey}")
        else:
            print(f"✅ 包含所有Survey字段")
            
        if extra_fields:
            print(f"ℹ️ 额外字段: {extra_fields}")
        
    except Exception as e:
        print(f"❌ 格式分析失败: {e}")

def suggest_format_improvements(crawl_result):
    """建议格式改进"""
    print("\n=== 格式改进建议 ===")
    
    if not crawl_result or not crawl_result['final_results']:
        print("❌ 没有结果可供分析")
        return
    
    sample_result = crawl_result['final_results'][0]
    
    print("需要进行的字段映射和添加:")
    
    # 字段映射
    field_mappings = [
        ("content", "txt", "将 content 字段重命名为 txt"),
        ("similarity_score", "similarity", "将 similarity_score 字段重命名为 similarity"),
    ]
    
    # 需要添加的字段
    additional_fields = [
        ("bibkey", "从 title 生成，使用 proc_title_to_str() 函数"),
        ("abstract", "从 txt 前500字符提取"),
        ("txt_token", "估算 txt 的 token 数量"),
        ("txt_length", "txt 字段的长度"),
        ("source_type", "标识为 'web_crawl'"),
        ("crawl_timestamp", "添加爬取时间戳"),
    ]
    
    print("\n1. 字段重命名:")
    for old_name, new_name, description in field_mappings:
        if old_name in sample_result:
            print(f"   ✓ {old_name} → {new_name}: {description}")
        else:
            print(f"   ❌ {old_name} 字段不存在")
    
    print("\n2. 需要添加的字段:")
    for field_name, description in additional_fields:
        if field_name in sample_result:
            print(f"   ✓ {field_name}: 已存在")
        else:
            print(f"   + {field_name}: {description}")
    
    print("\n3. 建议的增强格式:")
    enhanced_format = {
        "title": sample_result.get("title", ""),
        "url": sample_result.get("url", ""),
        "txt": sample_result.get("content", ""),  # 重命名
        "similarity": sample_result.get("similarity_score", 0),  # 重命名
        "bibkey": "需要生成",
        "abstract": "需要提取",
        "txt_token": "需要计算",
        "txt_length": len(sample_result.get("content", "")),
        "source_type": "web_crawl",
        "crawl_timestamp": "需要添加"
    }
    
    print(json.dumps(enhanced_format, ensure_ascii=False, indent=2))

async def main():
    """主测试函数"""
    print("=== 爬虫功能测试（跳过SERP搜索）===")
    print(f"服务器配置: {SERVER_CONFIG}")
    
    # 测试爬虫功能
    crawl_result = await test_crawler_with_test_urls()
    
    # 分析当前格式
    analyze_current_format(crawl_result)
    
    # 建议格式改进
    suggest_format_improvements(crawl_result)
    
    print("\n=== 测试完成 ===")
    
    # 保存测试结果
    if crawl_result:
        with open("test_crawler_result.json", "w", encoding="utf-8") as f:
            json.dump(crawl_result, f, ensure_ascii=False, indent=2)
        print("✅ 测试结果已保存到 test_crawler_result.json")

if __name__ == "__main__":
    asyncio.run(main())
