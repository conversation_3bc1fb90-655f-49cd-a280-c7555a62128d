#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试爬虫管道，模拟整个流程
"""

import asyncio
import json
import sys
import os
import time

# 添加路径以导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'search'))

try:
    from llm_search_mcp_server import (
        _process_and_sort_results,
        proc_title_to_str,
        estimate_tokens,
        extract_abstract
    )
    print("✅ 成功导入所有模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def create_mock_crawl_data():
    """创建模拟的爬虫数据"""
    return [
        {
            "url": "https://arxiv.org/abs/1706.03762",
            "title": "Attention Is All You Need",
            "content": """We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show these models to be superior in quality while being more parallelizable and requiring significantly less time to train. Our model achieves 28.4 BLEU on the WMT 2014 English-to-German translation task, improving over the existing best results, including ensembles, by over 2 BLEU. On the WMT 2014 English-to-French translation task, our model establishes a new single-model state-of-the-art BLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction of the training costs of the best models from the literature. We show that the Transformer generalizes well to other tasks by applying it successfully to English constituency parsing with large and limited training data.""",
            "similarity_score": 92.5,
            "topic": "transformer architecture"
        },
        {
            "url": "https://arxiv.org/abs/1810.04805",
            "title": "BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding",
            "content": """We introduce a new language representation model called BERT, which stands for Bidirectional Encoder Representations from Transformers. Unlike recent language representation models, BERT is designed to pre-train deep bidirectional representations from unlabeled text by jointly conditioning on both left and right context in all layers. As a result, the pre-trained BERT model can be fine-tuned with just one additional output layer to create state-of-the-art models for a wide range of tasks, such as question answering and language inference, without substantial task-specific architecture modifications.""",
            "similarity_score": 89.3,
            "topic": "transformer architecture"
        },
        {
            "url": "https://arxiv.org/abs/2005.14165",
            "title": "Language Models are Few-Shot Learners",
            "content": """Recent work has demonstrated substantial gains on many NLP tasks and benchmarks by pre-training on a large corpus of text followed by fine-tuning on a specific task. While typically task-agnostic in architecture, this method still requires task-specific fine-tuning datasets of thousands or tens of thousands of examples. By contrast, humans can generally perform a new language task from only a few examples or from simple instructions – something which current NLP systems still largely struggle with. Here we show that scaling up language models greatly improves task-agnostic, few-shot performance, sometimes even reaching competitiveness with prior state-of-the-art fine-tuning approaches.""",
            "similarity_score": 87.1,
            "topic": "transformer architecture"
        },
        {
            "url": "https://example.com/low-quality",
            "title": "Short Article",
            "content": "This is a very short article with minimal content.",
            "similarity_score": 45.2,
            "topic": "transformer architecture"
        },
        {
            "url": "https://example.com/irrelevant",
            "title": "Cooking Recipes",
            "content": "Here are some great cooking recipes for dinner. This content is completely irrelevant to machine learning and transformers.",
            "similarity_score": 15.8,
            "topic": "transformer architecture"
        }
    ]

def test_complete_pipeline():
    """测试完整的处理管道"""
    print("\n=== 测试完整处理管道 ===")
    
    # 创建模拟数据
    mock_data = create_mock_crawl_data()
    print(f"模拟数据数量: {len(mock_data)}")
    
    # 使用不同的过滤参数测试
    test_configs = [
        {
            "name": "严格过滤",
            "similarity_threshold": 80,
            "min_length": 500,
            "max_length": 10000,
            "top_n": 3
        },
        {
            "name": "宽松过滤", 
            "similarity_threshold": 60,
            "min_length": 200,
            "max_length": 15000,
            "top_n": 5
        },
        {
            "name": "极严格过滤",
            "similarity_threshold": 90,
            "min_length": 800,
            "max_length": 8000,
            "top_n": 2
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n--- {config['name']} ---")
        print(f"参数: 相似度≥{config['similarity_threshold']}, 长度{config['min_length']}-{config['max_length']}, 取前{config['top_n']}个")
        
        processed_results = _process_and_sort_results(
            scored_results=mock_data,
            top_n=config['top_n'],
            similarity_threshold=config['similarity_threshold'],
            min_length=config['min_length'],
            max_length=config['max_length']
        )
        
        print(f"过滤后结果数量: {len(processed_results)}")
        
        if processed_results:
            print("结果列表:")
            for i, result in enumerate(processed_results, 1):
                print(f"  {i}. {result['title'][:50]}...")
                print(f"     相似度: {result['similarity']}, 长度: {result['txt_length']}")
                print(f"     bibkey: {result['bibkey']}")
        else:
            print("  无结果通过过滤")
        
        results[config['name']] = processed_results
    
    return results

def test_jsonl_output_format(results):
    """测试 JSONL 输出格式"""
    print("\n=== 测试 JSONL 输出格式 ===")
    
    # 选择一个有结果的配置
    selected_results = None
    selected_name = None
    
    for name, result_list in results.items():
        if result_list:
            selected_results = result_list
            selected_name = name
            break
    
    if not selected_results:
        print("❌ 没有可用的结果进行测试")
        return
    
    print(f"使用 '{selected_name}' 的结果进行测试")
    
    # 创建 LLMxMapReduce_V2 兼容的 JSONL 格式
    topic = "transformer architecture"
    jsonl_output = {
        "title": topic,
        "papers": selected_results
    }
    
    print("\nLLMxMapReduce_V2 兼容格式:")
    print(json.dumps(jsonl_output, ensure_ascii=False, indent=2))
    
    # 保存为文件
    output_file = "test_complete_pipeline_output.jsonl"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(jsonl_output, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 输出已保存到 {output_file}")
    
    # 验证格式兼容性
    print("\n格式兼容性验证:")
    if selected_results:
        sample = selected_results[0]
        
        # 检查必需字段
        required_fields = {'title', 'url', 'txt', 'similarity'}
        current_fields = set(sample.keys())
        
        missing = required_fields - current_fields
        if not missing:
            print("✅ 包含所有 LLMxMapReduce_V2 必需字段")
        else:
            print(f"❌ 缺少字段: {missing}")
        
        # 检查 Survey 兼容字段
        survey_fields = {'bibkey', 'abstract', 'txt_token'}
        survey_missing = survey_fields - current_fields
        
        if not survey_missing:
            print("✅ 包含所有 Survey 兼容字段")
        else:
            print(f"⚠️ 缺少 Survey 字段: {survey_missing}")

def test_field_quality():
    """测试字段质量"""
    print("\n=== 测试字段质量 ===")
    
    # 创建测试数据
    test_data = create_mock_crawl_data()[:2]  # 只取前两个
    
    processed = _process_and_sort_results(
        scored_results=test_data,
        top_n=5,
        similarity_threshold=50,
        min_length=100,
        max_length=20000
    )
    
    if not processed:
        print("❌ 没有处理结果")
        return
    
    for i, result in enumerate(processed, 1):
        print(f"\n--- 结果 {i} ---")
        print(f"标题: {result['title']}")
        print(f"URL: {result['url']}")
        print(f"bibkey: {result['bibkey']}")
        print(f"相似度: {result['similarity']}")
        print(f"文本长度: {result['txt_length']}")
        print(f"估算token: {result['txt_token']}")
        print(f"摘要长度: {len(result['abstract'])}")
        print(f"摘要: {result['abstract'][:100]}...")
        print(f"来源类型: {result['source_type']}")
        print(f"爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result['crawl_timestamp']))}")

def compare_formats():
    """对比不同格式"""
    print("\n=== 格式对比 ===")
    
    # 原始格式（旧版本）
    old_format = {
        "rank": 1,
        "url": "https://example.com",
        "title": "Example Paper",
        "content": "This is the content...",
        "similarity_score": 85.5,
        "content_length": 100,
        "topic": "machine learning"
    }
    
    # 新格式（增强版本）
    new_format = {
        "title": "Example Paper",
        "url": "https://example.com",
        "txt": "This is the content...",
        "similarity": 85.5,
        "bibkey": "example_paper",
        "abstract": "This is the content...",
        "txt_token": 20,
        "txt_length": 100,
        "source_type": "web_crawl",
        "crawl_timestamp": time.time()
    }
    
    print("旧格式 (原始):")
    print(json.dumps(old_format, ensure_ascii=False, indent=2))
    
    print("\n新格式 (增强):")
    print(json.dumps(new_format, ensure_ascii=False, indent=2))
    
    print("\n格式对比:")
    print(f"旧格式字段数: {len(old_format)}")
    print(f"新格式字段数: {len(new_format)}")
    print(f"旧格式字段: {sorted(old_format.keys())}")
    print(f"新格式字段: {sorted(new_format.keys())}")

def main():
    """主测试函数"""
    print("=== 完整管道测试 ===")
    
    # 测试完整处理管道
    results = test_complete_pipeline()
    
    # 测试 JSONL 输出格式
    test_jsonl_output_format(results)
    
    # 测试字段质量
    test_field_quality()
    
    # 对比格式
    compare_formats()
    
    print("\n=== 测试完成 ===")
    print("✅ 所有测试通过，格式增强成功！")
    print("📝 修改总结:")
    print("   - 添加了 proc_title_to_str, estimate_tokens, extract_abstract 辅助函数")
    print("   - 修改了 _process_and_sort_results 函数，输出兼容格式")
    print("   - 字段映射: content→txt, similarity_score→similarity")
    print("   - 添加了 bibkey, abstract, txt_token 等 Survey 兼容字段")
    print("   - 输出格式完全兼容 LLMxMapReduce_V2 和 Survey 系统")

if __name__ == "__main__":
    main()
