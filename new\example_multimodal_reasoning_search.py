#!/usr/bin/env python3
"""
多模态推理综述文献搜索示例
通过LLM_search Host调用MCP架构进行学术文献搜索
"""

import asyncio
import json
import logging
import os
import sys
from typing import List, Dict

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_host import LLM_search

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MultimodalReasoningSurveySearcher:
    """多模态推理综述搜索器"""
    
    def __init__(self):
        """初始化搜索器，使用配置文件中的默认模型"""
        self.llm_search = LLM_search()
        logger.info("多模态推理综述搜索器初始化完成")
    
    def search_multimodal_reasoning_surveys(self) -> Dict:
        """搜索多模态推理综述文献"""
        print("🔍 开始搜索多模态推理综述文献...")
        
        # 定义搜索主题和描述
        topic = "多模态推理综述"
        description = """
        搜索关于多模态推理(Multimodal Reasoning)的综述文献，重点关注：
        1. 视觉-语言推理(Vision-Language Reasoning)
        2. 多模态大语言模型(Multimodal Large Language Models)
        3. 跨模态理解与推理(Cross-modal Understanding and Reasoning)
        4. 视觉问答(Visual Question Answering, VQA)
        5. 图像描述生成(Image Captioning)
        6. 多模态知识图谱推理
        7. 最新的多模态推理方法和技术进展
        
        优先搜索2022-2024年的最新综述论文和技术报告。
        """
        
        try:
            # 步骤1: 生成专业的学术搜索查询
            print("\n📝 步骤1: 生成学术搜索查询...")
            queries = self.llm_search.get_queries(topic=topic, description=description)
            
            print(f"✅ 生成了 {len(queries)} 个搜索查询:")
            for i, query in enumerate(queries, 1):
                print(f"  {i}. {query}")
            
            # 步骤2: 执行批量搜索
            print(f"\n🌐 步骤2: 执行批量学术搜索...")
            urls = self.llm_search.batch_web_search(
                queries=queries,
                topic=topic,
                top_n=30  # 获取更多结果以确保覆盖面
            )
            
            print(f"✅ 找到 {len(urls)} 个相关链接")
            
            # 步骤3: 爬取和分析内容
            print(f"\n📄 步骤3: 爬取和分析文献内容...")
            results = self.llm_search.crawl_urls(
                topic=topic,
                url_list=urls,
                top_n=15,  # 返回最相关的15篇文献
                similarity_threshold=75,  # 较高的相似度阈值
                min_length=500,  # 确保内容足够详细
                max_length=25000  # 允许较长的学术内容
            )
            
            print(f"✅ 成功分析了 {len(results)} 篇相关文献")
            
            return {
                "topic": topic,
                "description": description,
                "queries": queries,
                "total_urls": len(urls),
                "analyzed_papers": len(results),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"搜索过程中出现错误: {e}")
            return {
                "error": str(e),
                "topic": topic,
                "description": description
            }
    
    def search_specific_multimodal_topics(self) -> Dict:
        """搜索特定的多模态推理子领域"""
        print("\n🎯 搜索特定多模态推理子领域...")
        
        # 定义多个子领域搜索
        subtopics = [
            {
                "topic": "视觉语言预训练模型综述",
                "description": "搜索关于CLIP、DALL-E、GPT-4V等视觉语言预训练模型的综述文献"
            },
            {
                "topic": "多模态大语言模型评估",
                "description": "搜索关于多模态LLM评估方法、基准数据集和评价指标的研究"
            },
            {
                "topic": "具身智能多模态推理",
                "description": "搜索关于机器人、具身AI中的多模态推理和决策的研究"
            }
        ]
        
        all_results = {}
        
        for subtopic in subtopics:
            print(f"\n🔍 搜索子领域: {subtopic['topic']}")
            
            try:
                # 为每个子领域生成查询
                queries = self.llm_search.get_queries(
                    topic=subtopic['topic'],
                    description=subtopic['description']
                )
                
                # 执行搜索
                urls = self.llm_search.batch_web_search(
                    queries=queries[:3],  # 每个子领域使用前3个查询
                    topic=subtopic['topic'],
                    top_n=15
                )
                
                # 分析内容
                results = self.llm_search.crawl_urls(
                    topic=subtopic['topic'],
                    url_list=urls,
                    top_n=8,  # 每个子领域返回8篇文献
                    similarity_threshold=70,
                    min_length=400,
                    max_length=20000
                )
                
                all_results[subtopic['topic']] = {
                    "queries": queries,
                    "urls_found": len(urls),
                    "papers_analyzed": len(results),
                    "results": results
                }
                
                print(f"✅ {subtopic['topic']}: 找到 {len(results)} 篇相关文献")
                
            except Exception as e:
                logger.error(f"搜索子领域 {subtopic['topic']} 时出错: {e}")
                all_results[subtopic['topic']] = {"error": str(e)}
        
        return all_results
    
    def format_search_results(self, results: Dict) -> str:
        """格式化搜索结果为可读的报告"""
        if "error" in results:
            return f"❌ 搜索失败: {results['error']}"
        
        report = []
        report.append("=" * 80)
        report.append("📚 多模态推理综述文献搜索报告")
        report.append("=" * 80)
        
        report.append(f"\n🎯 搜索主题: {results['topic']}")
        report.append(f"\n📝 搜索描述: {results['description']}")
        
        report.append(f"\n📊 搜索统计:")
        report.append(f"  • 生成查询数: {len(results['queries'])}")
        report.append(f"  • 找到链接数: {results['total_urls']}")
        report.append(f"  • 分析文献数: {results['analyzed_papers']}")
        
        report.append(f"\n🔍 生成的搜索查询:")
        for i, query in enumerate(results['queries'], 1):
            report.append(f"  {i}. {query}")
        
        if results['results']:
            report.append(f"\n📄 分析的文献摘要:")
            for i, paper in enumerate(results['results'][:10], 1):  # 显示前10篇
                title = paper.get('title', '未知标题')
                url = paper.get('url', '未知链接')
                score = paper.get('score', 0)
                content_preview = paper.get('content', '')[:200] + "..."
                
                report.append(f"\n  📖 文献 {i}: {title}")
                report.append(f"     🔗 链接: {url}")
                report.append(f"     ⭐ 相关度: {score:.1f}")
                report.append(f"     📝 内容预览: {content_preview}")
        
        return "\n".join(report)
    
    def save_results_to_file(self, results: Dict, filename: str = None):
        """保存搜索结果到文件"""
        if filename is None:
            filename = f"multimodal_reasoning_survey_{len(results.get('results', []))}_papers.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 搜索结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存文件失败: {e}")

def main():
    """主函数 - 演示多模态推理综述搜索"""
    print("🚀 多模态推理综述文献搜索示例")
    print("=" * 60)
    
    # 创建搜索器
    searcher = MultimodalReasoningSurveySearcher()
    
    # 示例1: 综合搜索多模态推理综述
    print("\n📚 示例1: 综合搜索多模态推理综述文献")
    print("-" * 50)
    
    general_results = searcher.search_multimodal_reasoning_surveys()
    
    # 显示格式化结果
    report = searcher.format_search_results(general_results)
    print(report)
    
    # 保存结果
    searcher.save_results_to_file(general_results, "multimodal_reasoning_general_survey.json")
    
    # 示例2: 搜索特定子领域
    print("\n\n🎯 示例2: 搜索特定多模态推理子领域")
    print("-" * 50)
    
    specific_results = searcher.search_specific_multimodal_topics()
    
    # 显示子领域结果
    for subtopic, result in specific_results.items():
        if "error" not in result:
            print(f"\n📖 {subtopic}: 找到 {result['papers_analyzed']} 篇文献")
            if result['results']:
                for i, paper in enumerate(result['results'][:3], 1):  # 显示前3篇
                    title = paper.get('title', '未知标题')
                    score = paper.get('score', 0)
                    print(f"  {i}. {title} (相关度: {score:.1f})")
        else:
            print(f"\n❌ {subtopic}: {result['error']}")
    
    # 保存子领域结果
    with open("multimodal_reasoning_subtopics.json", 'w', encoding='utf-8') as f:
        json.dump(specific_results, f, ensure_ascii=False, indent=2)
    print(f"\n✅ 子领域搜索结果已保存到: multimodal_reasoning_subtopics.json")
    
    print("\n🎉 多模态推理综述文献搜索完成！")
    print("\n📋 使用说明:")
    print("1. 确保已设置 OPENAI_API_KEY 和 SERP_API_KEY 环境变量")
    print("2. 搜索结果已保存为JSON文件，可用于进一步分析")
    print("3. 可以修改搜索参数来获得不同的结果")
    print("4. 支持自定义搜索主题和描述")

if __name__ == "__main__":
    main()
