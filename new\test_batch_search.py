#!/usr/bin/env python3
"""
测试batch_web_search方法
"""
import os
import sys
import json

# 设置环境变量
os.environ["SERPAPI_KEY"] = "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"

# 添加项目路径
sys.path.insert(0, os.path.abspath("."))

from src.search.LLM_search import LLM_search

def test_batch_web_search():
    """测试batch_web_search方法"""
    print("🧪 测试batch_web_search方法...")
    
    try:
        # 创建LLM_search实例
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            engine="google",
            each_query_result=3  # 减少每个查询的结果数量以便调试
        )
        
        print(f"🔑 serpapi_key: {llm_search.serpapi_key[:10]}..." if llm_search.serpapi_key else "❌ 没有serpapi_key")
        
        # 测试查询列表
        queries = [
            "Transformer Attention机制 原理",
            "Self-Attention (自注意力) 机制 原理 公式 推导",
            "Multi-Head Attention (多头注意力) 机制 原理 作用"
        ]
        
        topic = "transformer attention mechanism"
        
        print(f"🔍 测试查询列表: {queries}")
        print(f"📝 主题: {topic}")
        
        # 调用batch_web_search
        print("\n🚀 开始batch_web_search...")
        urls = llm_search.batch_web_search(queries=queries, topic=topic, top_n=5)
        
        print(f"📊 返回URL类型: {type(urls)}")
        print(f"🔢 返回URL数量: {len(urls)}")
        
        if urls:
            print("✅ 成功获取URL:")
            for i, url in enumerate(urls[:5]):
                print(f"  {i+1}. {url}")
        else:
            print("❌ 没有获取到任何URL")
            
        return urls
        
    except Exception as e:
        print(f"❌ batch_web_search测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_single_query_in_batch():
    """测试单个查询在batch中的处理"""
    print("\n🧪 测试单个查询在batch中的处理...")
    
    try:
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI",
            engine="google",
            each_query_result=3
        )
        
        # 测试单个查询
        query = "Transformer Attention机制 原理"
        print(f"🔍 测试单个查询: {query}")
        
        # 直接调用web_search
        print("📞 直接调用web_search...")
        result = llm_search.web_search(query)
        print(f"📊 web_search结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"🔢 web_search结果数量: {len(result)}")
            print("📄 web_search结果键:", list(result.keys()))
            
            # 检查结果结构
            for key, value in list(result.items())[:2]:
                print(f"  {key}: {value.get('url', 'No URL')} - {value.get('title', 'No Title')[:50]}...")
        else:
            print(f"📄 web_search结果: {result}")
            
        # 现在测试在batch中的处理
        print("\n📞 在batch中调用相同查询...")
        urls = llm_search.batch_web_search(queries=[query], topic="transformer attention", top_n=5)
        print(f"📊 batch结果类型: {type(urls)}")
        print(f"🔢 batch结果数量: {len(urls)}")
        
        if urls:
            print("✅ batch成功获取URL:")
            for i, url in enumerate(urls):
                print(f"  {i+1}. {url}")
        else:
            print("❌ batch没有获取到任何URL")
            
    except Exception as e:
        print(f"❌ 单个查询测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始batch_web_search测试")
    test_single_query_in_batch()
    test_batch_web_search()
    print("🏁 测试结束")
