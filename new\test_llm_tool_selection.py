#!/usr/bin/env python3
"""
测试Host中LLM自主选择和调用MCP工具的完整流程
验证 Host -> Client -> Server 这一套流程是否正确
"""

import asyncio
import json
import logging
import os
import sys

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.llm_search_host import create_llm_search_host

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_llm_autonomous_tool_selection():
    """测试LLM自主选择工具的完整流程"""
    print("🤖 测试Host中LLM自主选择和调用MCP工具")
    print("=" * 60)
    
    try:
        # 1. 创建Host
        print("\n1️⃣ 创建LLMSearchHost...")
        host = create_llm_search_host()
        print(f"✅ Host创建成功 - 模型: {host.model}")
        
        # 2. 测试LLM自主工具选择 - 简单任务
        print("\n2️⃣ 测试简单任务：生成搜索查询")
        print("   任务描述：为'机器学习'主题生成5个搜索查询")
        
        task_description = """
请为研究主题'机器学习'生成5个高质量的搜索查询。
这些查询应该涵盖机器学习的基础概念、算法和应用。
"""
        
        result = await host._llm_select_and_call_tool(
            task_description,
            topic="机器学习",
            description="机器学习基础概念和算法",
            num_queries=5
        )
        
        print(f"✅ LLM工具选择结果:")
        if result:
            print(f"   返回数据类型: {type(result)}")
            if isinstance(result, dict):
                for key, value in result.items():
                    if key == 'queries' and isinstance(value, list):
                        print(f"   {key}: {len(value)} 个查询")
                        for i, query in enumerate(value[:3], 1):  # 显示前3个
                            print(f"     {i}. {query}")
                        if len(value) > 3:
                            print(f"     ... 还有 {len(value)-3} 个查询")
                    else:
                        print(f"   {key}: {str(value)[:100]}...")
            else:
                print(f"   结果: {str(result)[:200]}...")
        else:
            print("   ❌ 没有返回结果")
            
        # 3. 测试LLM自主工具选择 - 复杂任务
        print("\n3️⃣ 测试复杂任务：完整搜索流程")
        print("   任务描述：为'深度学习'执行完整的文献搜索")
        
        complex_task = """
请为研究主题'深度学习'执行完整的学术文献搜索流程：

1. 首先分析主题，生成多样化的搜索查询
2. 使用这些查询进行网络搜索，获取相关文献URL
3. 如果找到URL，可以尝试爬取部分内容进行分析

目标：获取3-5篇高质量的深度学习相关文献信息
"""
        
        complex_result = await host._llm_select_and_call_tool(
            complex_task,
            topic="深度学习",
            description="深度学习算法、架构和应用研究",
            top_n=5
        )
        
        print(f"✅ 复杂任务LLM工具选择结果:")
        if complex_result:
            print(f"   返回数据类型: {type(complex_result)}")
            if isinstance(complex_result, dict):
                for key, value in complex_result.items():
                    if isinstance(value, list):
                        print(f"   {key}: {len(value)} 个项目")
                        if value and isinstance(value[0], dict):
                            # 显示第一个项目的结构
                            first_item = value[0]
                            print(f"     第一个项目字段: {list(first_item.keys())}")
                    else:
                        print(f"   {key}: {str(value)[:100]}...")
            else:
                print(f"   结果: {str(complex_result)[:200]}...")
        else:
            print("   ❌ 没有返回结果")
            
        # 4. 测试错误处理
        print("\n4️⃣ 测试错误处理：无效任务")
        print("   任务描述：一个模糊不清的任务")
        
        invalid_task = "请帮我做一些事情"
        
        try:
            invalid_result = await host._llm_select_and_call_tool(
                invalid_task
            )
            print(f"✅ 错误处理结果: {type(invalid_result)}")
            if invalid_result:
                print(f"   LLM处理了模糊任务: {str(invalid_result)[:100]}...")
            else:
                print("   LLM正确地返回了空结果")
        except Exception as e:
            print(f"✅ 正确捕获错误: {e}")
            
        # 5. 验证工具选择的智能性
        print("\n5️⃣ 验证LLM工具选择的智能性")
        
        # 获取可用工具
        available_tools = await host._get_available_tools()
        tool_names = [tool['name'] for tool in available_tools]
        print(f"   可用工具: {tool_names}")
        
        # 测试不同类型的任务，看LLM是否选择合适的工具
        test_cases = [
            {
                "task": "为'人工智能'生成搜索查询",
                "expected_tool": "generate_search_queries"
            },
            {
                "task": "搜索关于'神经网络'的网页",
                "expected_tool": "web_search"
            },
            {
                "task": "分析搜索结果的相关性",
                "expected_tool": "analyze_search_results"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   测试案例 {i}: {test_case['task']}")
            try:
                # 这里我们不实际执行，只是测试LLM是否能理解任务
                result = await host._llm_select_and_call_tool(
                    test_case['task'],
                    topic="测试主题"
                )
                print(f"     ✅ LLM成功处理任务")
            except Exception as e:
                print(f"     ⚠️ 任务处理异常: {str(e)[:100]}...")
        
        # 6. 清理资源
        print("\n6️⃣ 清理资源...")
        await host._cleanup_client()
        print("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"LLM tool selection test failed: {e}", exc_info=True)
        return False

async def test_direct_llm_request():
    """测试直接从Host发出LLM请求让其选择工具"""
    print("\n🎯 测试直接LLM请求选择工具")
    print("=" * 60)
    
    try:
        host = create_llm_search_host()
        
        # 直接测试LLM的工具选择能力
        print("\n📝 向LLM发送任务：")
        task = """
我需要研究'计算机视觉'这个主题。请你：
1. 分析这个主题
2. 选择合适的工具
3. 执行必要的操作来获取相关信息

请自主决定使用哪些工具以及如何使用它们。
"""
        
        print(f"任务描述: {task}")
        print("\n🤖 LLM开始自主选择和调用工具...")
        
        # 让LLM自主选择工具
        result = await host._llm_select_and_call_tool(
            task,
            topic="计算机视觉",
            description="计算机视觉算法、技术和应用"
        )
        
        print("\n📊 LLM自主工具选择结果:")
        if result:
            print(f"✅ 成功获得结果")
            print(f"   结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print(f"   结果字段: {list(result.keys())}")
                
                # 详细分析结果
                for key, value in result.items():
                    if isinstance(value, list):
                        print(f"   {key}: 包含 {len(value)} 个项目")
                        if value:
                            if isinstance(value[0], str):
                                print(f"     示例: {value[0][:50]}...")
                            elif isinstance(value[0], dict):
                                print(f"     项目结构: {list(value[0].keys())}")
                    elif isinstance(value, str):
                        print(f"   {key}: {value[:100]}...")
                    else:
                        print(f"   {key}: {value}")
            else:
                print(f"   结果内容: {str(result)[:300]}...")
                
        else:
            print("❌ LLM没有返回结果")
            
        await host._cleanup_client()
        return True
        
    except Exception as e:
        print(f"❌ 直接LLM请求测试失败: {e}")
        logger.error(f"Direct LLM request test failed: {e}", exc_info=True)
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试Host中LLM自主工具选择流程")
    print("验证 Host -> Client -> Server 完整链路")
    print("=" * 80)
    
    results = {}
    
    # 1. 测试LLM自主工具选择
    results['autonomous_selection'] = await test_llm_autonomous_tool_selection()
    
    # 2. 测试直接LLM请求
    results['direct_llm_request'] = await test_direct_llm_request()
    
    # 总结结果
    print("\n📊 测试结果总结:")
    print("=" * 50)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:25} : {status}")
    
    all_passed = all(results.values())
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有测试通过！")
        print("\n✅ 验证确认:")
        print("  - Host中LLM可以自主选择工具")
        print("  - Host -> Client -> Server 链路正常")
        print("  - LLM能够理解任务并调用合适的MCP工具")
        print("  - 工具调用结果正确返回到Host")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
