#!/usr/bin/env python3
"""
Host 简化测试
测试基本功能和一个完整的搜索流程
"""

import asyncio
import logging
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_host import LLMSearchHostV3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_host_initialization():
    """测试Host初始化"""
    print("🧪 测试Host初始化...")
    try:
        host = LLMSearchHostV3()
        print("✅ Host初始化成功")
        return host
    except Exception as e:
        print(f"❌ Host初始化失败: {e}")
        return None

async def test_mcp_connection(host):
    """测试MCP连接"""
    print("🧪 测试MCP连接...")
    try:
        client = await host._create_mcp_client()
        print("✅ MCP客户端创建成功")
        
        # 测试工具列表
        tools = await client.list_tools()
        print(f"✅ 获取到 {len(tools)} 个工具")
        
        expected_tools = ["generate_search_queries", "web_search", "crawl_urls", "analyze_search_results"]
        for tool in expected_tools:
            if any(t["name"] == tool for t in tools):
                print(f"  ✅ 工具 {tool} 可用")
            else:
                print(f"  ❌ 工具 {tool} 不可用")
        
        await client.disconnect()
        print("✅ MCP连接测试完成")
        return True
    except Exception as e:
        print(f"❌ MCP连接测试失败: {e}")
        return False

async def test_simple_search(host):
    """测试简单搜索"""
    print("🧪 测试简单搜索...")
    try:
        start_time = time.time()
        
        result = await host.search(
            topic="machine learning",
            description="搜索机器学习基础知识"
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 搜索完成，耗时: {execution_time:.2f}秒")
        
        if result and "processing_metadata" in result:
            metadata = result["processing_metadata"]
            print(f"  搜索轮数: {metadata.get('turns', 0)}")
            print(f"  使用工具: {metadata.get('tools_used', [])}")
            print(f"  结果数量: {len(result.get('final_results', []))}")
            
            if result.get("final_results"):
                print("  前3个结果:")
                for i, res in enumerate(result["final_results"][:3]):
                    print(f"    {i+1}. {res.get('title', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"❌ 简单搜索失败: {e}")
        return False

async def test_error_handling(host):
    """测试错误处理"""
    print("🧪 测试错误处理...")
    try:
        # 测试空主题
        result = await host.search(topic="", description="空主题测试")
        print("✅ 空主题处理正常")
        return True
    except Exception as e:
        print(f"⚠️ 空主题处理异常（可能是预期的）: {e}")
        return True  # 错误处理也算通过

def print_summary(results):
    """打印测试总结"""
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r[1])
    
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
    else:
        print(f"⚠️ {total_tests-passed_tests} 个测试失败")
    
    print("="*60)

async def main():
    """主测试函数"""
    print("🚀 Host 简化测试开始")
    print("="*60)
    
    results = []
    
    # 1. 测试Host初始化
    host = await test_host_initialization()
    results.append(("Host初始化", host is not None))
    
    if not host:
        print("❌ Host初始化失败，终止测试")
        return
    
    # 2. 测试MCP连接
    mcp_ok = await test_mcp_connection(host)
    results.append(("MCP连接", mcp_ok))
    
    if not mcp_ok:
        print("❌ MCP连接失败，跳过搜索测试")
    else:
        # 3. 测试简单搜索
        search_ok = await test_simple_search(host)
        results.append(("简单搜索", search_ok))
        
        # 4. 测试错误处理
        error_ok = await test_error_handling(host)
        results.append(("错误处理", error_ok))
    
    # 打印总结
    print_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
